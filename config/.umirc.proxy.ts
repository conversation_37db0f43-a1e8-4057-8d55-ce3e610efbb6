/*
 * @Author: wendy
 * @Date: 2020-08-26 09:45:49
 * @LastEditors: <Your Name>
 * @LastEditTime: 2024-10-14 10:57:27
 * @Description:
 */
const { environment } = process.env;
const domain = {
  'cctc':'https://aq.cctc.cn',
  'environment': `https://zz-test${environment}.pinming.org`
}

const target = domain['cctc'];

export default {
  '/home/': {
    target,
    // target: `http://*************:8084`,
    // pathRewrite: {"^/home" : ""},
    changeOrigin: true,
  },
  '/agilebpm/': {
    target,
    changeOrigin: true,
  },
  '/taskcenter/': {
    target,
    changeOrigin: true,
  },
  '/oa/': {
    target,
    changeOrigin: true,
  },
  '/sso/': {
    target,
    changeOrigin: true,
  },
  '/company/': {
    target,
    changeOrigin: true,
  },
  '/project/': {
    target,
    changeOrigin: true,
  },
  '/lib/': {
    target,
    changeOrigin: true,
  },
  '/passport/': {
    target,
    changeOrigin: true,
  },
  '/plug/': {
    target,
    changeOrigin: true,
  },
  '/login/': {
    target,
    changeOrigin: true,
  },
  '/lw/': {
    target,
    changeOrigin: true,
  },
  '/videoCenter/api/': {
    target,
    // target: `http://172.16.66.214:8080`,
    changeOrigin: true,
  },
  '/gddnElevator/': {
    target,
    // target: `http://172.16.66.214:9098`,
    changeOrigin: true,
  },
  '/gddnDust/': {
    target,
    // target: `http://172.16.66.72:8080`,
    // target: 'http://172.16.10.80:8080',  // 荣挺
    changeOrigin: true,
    // pathRewrite: {"^/gddnDust" : ""},
  },
  '/gddnTower/': {
    target,
    // target: `http://172.16.66.72:8080`,
    changeOrigin: true,
  },
  '/schedule-plan/': {
    target,
    changeOrigin: true,
  },
  '/inspect/': {
    target,
    changeOrigin: true,
  },
  '/corPerson/': {
    target,
    changeOrigin: true,
  },
  '/person/': {
    target,
    changeOrigin: true,
  },
  '/dataBoard/': {
    target,
    changeOrigin: true,
  },
  '/platformConfig/': {
    target,
    // target: 'http://172.16.11.29:7088',
    changeOrigin: true,
    // pathRewrite: {"^/platformConfig" : ""},
  },
  '/epidemic/': {
    target,
    // target: 'http://172.16.11.125:8202',
    changeOrigin: true,
    // pathRewrite: {"^/epidemic" : ""},
  },
  '/zhgd-person/': {
    target,
    // target: 'http://172.16.11.125:8202',
    changeOrigin: true,
    // pathRewrite: {"^/zhgd-person" : ""},
  },
  '/gddnMarkRoom/': {
    target,
    // target: 'http://172.16.10.80:9096',
    changeOrigin: true,
    // pathRewrite: {"^/gddnMarkRoom" : ""},
  },
  '/mechatronics/': {
    target,
    // target: `http://172.16.10.45:8085`,
    changeOrigin: true,
    // pathRewrite: { '^/mechatronics': '' },
  },
  '/api-gateway/': {
    target,
    changeOrigin: true,
  },
  '/bim/': {
    target,
    // target: `http://172.16.10.45:8080`,
    changeOrigin: true,
    // pathRewrite: { '^/bim': '' },
  },
  '/bimfile/': {
    target,
    // target: `http://*************:8084`,
    // pathRewrite: { '^/bimfile': '' },
    changeOrigin: true,
  },
  '/quality-forms/api': {
    target,
    changeOrigin: true,
  },
  '/quality-goal/api': {
    target,
    changeOrigin: true,
  },
  '/metapen-analysis/api': {
    target,
    changeOrigin: true,
  },
  '/schedule-management/': {
    target,
    changeOrigin: true,
  },
  '/dataview/api/': {
    target,
    changeOrigin: true,
  },
  '/news-center/': {
    target,
    changeOrigin: true,
  },
};

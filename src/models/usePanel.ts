import { useCallback, useState } from 'react';
import { findPanelList, findUserList, IPanel } from '@/services/api';
import { omit } from 'lodash';
import panelStatic from '@/panel/panel';
import { useUpdateEffect } from 'react-use';

// 有些面板已经迁移到其它地方，但是服务端的返回的数据还有相关数据，需要移除掉
const ignorePanels = ['panel_message_center', 'panel_task_center']

export default function usePanel() {
  // 所有面版
  const [panels, setPanels] = useState<IPanel[]>([]);
  // 用户启用的面版
  const [panelList, setPanelList] = useState<IPanel[]>([]);
  const [unUsePanelList, setUnUsePanelList] = useState<IPanel[]>([]);
  const [showMessage] = useState(1);
  const [showEdit] = useState(true);
  const [loading, setLoading] = useState(true);
  const getLocation: any = (item: IPanel) => {
    const panelStaticDetails = panelStatic[item.panelNo];
    let location = omit(panelStaticDetails, ['Component']);
    if (item.location) {
      location = {...location, ...JSON.parse(item.location)}
    }
    return {
      ...item,
      ...panelStaticDetails,
      location,
    }
  };
  const getPanels = useCallback(() => {
    setLoading(true);
    findPanelList().then((res: { list: IPanel[] }) => {
      const panelArr:IPanel[] = res.list
        .filter(item => !ignorePanels.includes(item.panelNo))
        .map((item: any) => {
          return getLocation(item)
      });
      setPanels(panelArr);
      findUserList().then((res: { list: IPanel[] }) => {
        let list:IPanel[] = res.list
          .filter(item => !ignorePanels.includes(item.panelNo))
          .map((item: any) => {
          return getLocation(item);
        });
        setPanelList(list);
        setLoading(false);
      });
    });
  }, []);
  useUpdateEffect(() => {
    const codes = panelList.map((item: IPanel) => {
      return item.panelNo
    });
    const list = panels.filter(item => !codes.includes(item.panelNo) && !item.system);
    setUnUsePanelList(list);
  },[panelList]);
  return {
    panels,
    panelList,
    unUsePanelList,
    setPanelList,
    showMessage,
    getPanels,
    showEdit,
    loading,
  }
}

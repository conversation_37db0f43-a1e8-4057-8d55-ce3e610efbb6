
html, body, #root {
  height: 100%;
}

body {
  margin: 0;
}
.SortableContainer-moveImg{
  background: #fff;
  height: 100%;
  box-shadow: 0 0 8px 0 rgba(0,0,0,0.2);
  display: flex;
  flex-direction: column;
  position: relative;
}
.SortableContainer-moveImg >div{
  height: 100%;
  cursor: move;
}
.SortableContainer-moveImg .panelEditMask{
  cursor: move !important;
}
.SortableContainer-moveImg .panelEditButton {
  display: none;
}
.layoutPanelPm {
  height: 100%;
  background: none !important;
}
.layoutPanelPm .ant-layout-header{
  background: #fff !important;
  padding: 0 !important;
  height: auto !important;
  line-height: normal !important;
}
.layoutPanelPm .ant-layout-content{
  flex: 1 !important;
}
.panelTaskManager .ant-tabs-tab{
  border: none !important;
  background: none !important;
  padding: 8px 20px 8px 0 !important;
}


/* 定义隔行变色的样式 */
.homepanel-table-even-row {
  background-color: #f9f9f9;
  /* 偶数行颜色 */

  .homepanel-check-table-cell-fix-left, .homepanel-check-table-cell-fix-right{
    background-color: #f9f9f9;
  }
}

.homepanel-table-odd-row {
  background-color: #ffffff;
  /* 奇数行颜色 */
}

.homepanel-check-table-thead > tr > th{
  background: #f1f1f1 !important;
}

/* 去掉自定义类名 no-border-table 的所有边框 */
.homepanel-check-table-tbody > tr > td,.homepanel-check-table-thead > tr > th {
  border:none !important;
}
export const isOther = (url: string)=>{
  return url.startsWith('http') || url.startsWith('https')
}
export const windowOpen = (url: string, target = '_blank') => {
  if(!url) return
  const a = document.createElement('a'); // 创建a对象
  // 需要完整链接
  if(isOther(url)){
  }else{
    url = `${window.location.origin}/console${url}`;
  }
  a.setAttribute('href', url);
  a.setAttribute('target', target);
  document.body.appendChild(a);
  setTimeout(() => {
    document.body.removeChild(a);
  }, 300);
  a.click(); // 执行当前对象
};

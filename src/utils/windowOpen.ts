export const windowOpen = (url: string, target = '_blank') => {
  const a = document.createElement('a'); // 创建a对象
  // history 是 browser模式 没有#号
  // 需要完整链接
  if (!url.includes('#')) {
    if (url.startsWith('//')) {
      url = url.replace('/', '');
      url = `${window.location.origin}${url}`;
    } else {
      url = `${window.location.origin}${url}`;
    }
  }
  a.setAttribute('href', url);
  a.setAttribute('target', target);
  document.body.appendChild(a);
  setTimeout(() => {
    document.body.removeChild(a);
  }, 300);
  a.click(); // 执行当前对象
};

<!DOCTYPE html>
<html lang="zh">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0"/>
    <title>title</title>
  </head>
  <body>
		<script>
			if (!! document.documentMode) {
					// alert('当前浏览器是IE且版本介于8/9/10/11之间，文档模式是：' + document.documentMode);
					// 注意，虽然 documentMode 是IE8才开始有，但是documentMode最低可以是7是5，因为可以模拟低版本
					// document.body.innerHTML = '<div class="v-ie9"><p>暂不支持ie11及以下版本</p><p><span>请使用</span><a href="http://rj.baidu.com/soft/detail/14744.html?ald" target="_blank">chrome浏览器</a>，<a href="http://rj.baidu.com/soft/detail/11843.html" target="_blank">火狐浏览器</a>，如果是360，QQ等国产浏览器请切换到极速模式</p></div>'
					setTimeout(function(){
						console.log(document.getElementById('root-slave'))
						document.getElementById('root-slave').innerHTML='<div style="padding: 20px" class="v-ie9"><p>首页看板暂不支持ie11及以下版本</p><p><span>请使用</span><a href="http://rj.baidu.com/soft/detail/14744.html?ald" target="_blank">chrome浏览器</a>，<a href="http://rj.baidu.com/soft/detail/11843.html" target="_blank">火狐浏览器</a>，如果是360，QQ等国产浏览器请切换到极速模式</p></div>'
					}, 2000);
				}
		</script>
	</body>
</html>

#root-slave {
  width: 100%;
  height: 100%;
}

.wrapper {
  padding: 16px;
  width: 100%;
  height: 100%;
  background-color: #eef4f6;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
	position: relative;
	.wrapper-mask{
		position: absolute;
		top:0;
		left: 0;
		right: 0;
		bottom: 0;
		z-index: 999;
	}
  .top {
    width: 100%;
    height: 70%;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .top_left {
      width: calc(66.66% - 5px);
      height: 100%;
      position: relative;

      .top_left_bottom {
        height: 100%;

        .Nopermission {
          height: 100%;
          width: 100%;
          position: relative;
          background-image: url(./images/bg_2.png);

          .img-bg {
            position: absolute;
            top: 50%;
            left: 50%;
						transform: translate(-50%, -50%);
          }

          span {
            color: #fff;
          }
        }

        .Notice {
          a {
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 40px;
          }

          position:absolute;
          font-size: 16px;
          height: 40px;
          bottom:0px;
          padding: 0 16px;
          background-color:rgba(0, 60, 123, 0.8);
          z-index: 1;
          width: 100%;

        }

        .isNotice {
          span {
            color: #F3F9FF;
          }

          position:absolute;
          font-size: 18px;
          height: 40px;
          bottom:0px;
          color:#fff;
          text-align: center;
          margin-bottom: 75px;
          z-index: 1;
          width: 100%;

          .RightOutlined {
            float: right;
            margin: 12px 10px 0px 0px;
          }
        }
      }
    }

    .top_right {
      width: calc(33.33% - 10px);
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;

      .subtop_right {
				cursor: pointer;
        width: 100%;
        height: calc(50% - 8px);
        background-image: url(./images/bg_1.png);
        background-repeat: round;
        position: relative;

        .img-bg {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }

        .subtop_right_bottom {

          a {
            color: #fff;
          }

          display: flex;
          align-items: center;
          justify-content: space-between;
          font-size: 16px;
          height: 40px;
          position:absolute;
          bottom:0px;
          color:#fff;
          padding: 0 16px;
          background-color: rgba(0, 0, 0, 0.3);
          z-index: 1;
          width: 100%;
        }
      }
    }
  }

  .bottom {
    width: 100%;
    height: 30%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 16px;

    .Sub_bottom {
      width: calc(33.33% - 10px);
      height: 100%;
      position: relative;
      background-image: url(./images/bg_1.png);
      background-repeat: round;
			cursor: pointer;
      .img-bg {
        position: absolute;
        top: 50%;
        left: 50%;
				transform: translate(-50%, -50%);
      }

      .Sub_bottom_bottom {
        a {
          color: #fff;
        }

        display: flex;
        align-items: center;
        justify-content: space-between;
        position:absolute;
        font-size: 16px;
        height: 40px;
        bottom:0px;
        color:#fff;
        padding: 0 16px;
        background-color: rgba(0, 0, 0, 0.3);
        z-index: 1;
        width: 100%;
        cursor: pointer;
      }
    }
  }
}

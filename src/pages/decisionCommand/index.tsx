import React, { useState, useEffect } from 'react';
import { useEffectOnce } from 'react-use';
import { message } from 'antd';
import { RightOutlined } from '@ant-design/icons';
import { getStandardList, getPlugAuthority, decisionMakingGetConfig, getPlugStatusType } from '@/services/dataBoard';
import './index.less'
import Img1 from './images/plug_bg5.png'
import Img2 from './images/plug_bg2.png'
import Img3 from './images/plug_bg1.png'
import Img4 from './images/plug_bg4.png'
import Img5 from './images/plug_bg3.png'
import Img6 from './images/开发中.png'
import _SYSTEM01 from './images/数智工地应用报告.png'
import _SYSTEM02 from './images/应用考核评价.png'
import _SYSTEM03 from './images/项目诊断.png'
import { getCurrentUser } from '@/services/api';
import { useIntl } from 'umi';
import fileDownloadUrl from "@/services/fileDownloadUrl";
import plugLogo from './images/plug.png';
import { getToken } from '@/services/home';

const Icon = (props: any) => {
	const { fileUuid } = props;
	const [src, setSrc] = useState('');
	useEffectOnce(() => {
		console.log(props)
		if (props.obj.appId.includes('_SYSTEM')) {
			console.log(props.obj.appId)
			switch (props.obj.appId) {
				case '_SYSTEM01':
					setSrc(_SYSTEM01);
					break;
				case '_SYSTEM02':
					setSrc(_SYSTEM02);
					break;
				case '_SYSTEM03':
					setSrc(_SYSTEM03);
					break;
			}
			return;
		}
		if (fileUuid) {
			fileDownloadUrl(1, fileUuid).then((res: string) => {
				console.log('logo--------', res)
				setSrc(res);
			});
		} else {
			setSrc(plugLogo);
		}
	});
	return (
		<img alt='' src={src} className="img-bg" style={{width: '100px', height: '100px'}}/>
	);
}

export default () => {
	const intl = useIntl();
	const currentUser = getCurrentUser()
	const [src, setSrc] = useState('');
	const [monitors, setMonitors] = useState();
	const [isConfig, setIsconfig] = useState(false);
	const mptRoute = ['sf'];
	const panshiAppDom = document.getElementById('panshiApp');
	// 数据appName以后端返回的显示，这里只是做开发提示，无需写国际化
	const defaultAppList = [
		{ appName: '', appId: '_SYSTEM01' }, // 数智工地应用报告
		{ appName: '', appId: 'spzx' }, // 视频监控
		{ appName: '', appId: '_SYSTEM02' }, // 应用考核评价
		{ appName: '', appId: '_SYSTEM03' }, // 项目诊断
		{ appName: '', appId: 'assessment' } // 评价中心
	]
	const [appList, setappList] = useState<any>(defaultAppList);
	const [loading, setloading] = useState(true)
	useEffectOnce(() => {
		console.log('页面网址参数', window.location)
		if (window.location.href.includes('isConfig=true')) {
			setIsconfig(true)
		}
	})
	useEffect(() => {
		getStandardList().then(res => {
			if (res) {
				getActiveScreen(res.data.board)
			}
		})
	}, []);
	const getActiveScreen = (boardList: any) => {
		decisionMakingGetConfig().then((res) => {
			if (res.errorMsg) {
				message.error(res.errorMsg);
			} else {
				// 大屏信息
				let screenObj = res.data.find((item: any) => { return item.type === 1 })
				if (screenObj) {
					screenObj = boardList.find((item: any) => { return item.boardId == screenObj.appId }) // 由于数据结构统一boardId被后端改成appId了
				} else {
					screenObj = boardList[0] ? boardList[0] : {}
				}
				console.log('大屏配置和应用配置列表', res.data, '当前显示的大屏信息', screenObj)
				setMonitors(screenObj.boardId)
				if (screenObj.versionType === 1) {
					setSrc(`/dataBoard/#/dataV/preview?boardId=${screenObj.boardId}&isCloudSupervisor=${screenObj.isCloudSupervisor}&version=${screenObj.versionSn}&background`)
				} else {
					setSrc(`/dataBoard/#/pageManger/pageView?boardId=${screenObj ? `${screenObj.boardId}` : ''}&background`)
				}
				// 边上应用列表
				const appList = res.data.filter((item: any) => { return item.type === 2 })
				console.log('过滤后需要显示的appList', appList)
				setappList(appList)
			}
			setloading(false)
		}).catch((e) => {
			setloading(false)
		})
	}
	const go = (obj: any) => {
		if (obj.appId === '_SYSTEM01' || obj.appId === '_SYSTEM02' || obj.appId === '_SYSTEM03') {
			// message.info('正在开发中，敬请期待...');
			window.history.pushState({}, '', '/console/panel/under');
		} else {
			getPlugStatusType({ plugNo: obj.appId, projectId: currentUser.pjId || '' }).then(res => {
				if (res.errorMsg) return;
				switch (res.data) {
					// 允许跳转
					case 1:
						handlePush(obj);
						break;
					// 暂未开启该应用
					case 2:
						message.error(intl.formatMessage({ id: 'APP_NOT_OPEN' }));
						break;
					// 暂未开通该应用
					case 3:
						message.error('暂未开通该应用');
						break;
					// 该应用正在开发中
					case 4:
						window.history.pushState({}, '', '/console/panel/under');
						break;
				}
			});
		}
	}
	// 根据viewType，plugType等字段判断加载页面方式
	const handlePush = (obj: any) => {
		console.log('有权限的情况去往地址详情', obj)
		// window.history.pushState({}, '', '/console' + obj.url);
		handleRemoveIframe(obj.plugType);
		// 新窗口打开
		if (obj.viewType === 2) {
			window.open(obj.url)
			return
		}
		if (obj.plugType === 2) {
			renderPlugIframe(obj.url);
			return
		}
		// 中台的应用打开方式 frontUrl是微前端架构
		const href = `/console${obj.url}`;
		if (obj.frontUrl) {
			const u = navigator.userAgent;
			const isIe = u.indexOf('Trident') > -1;
			if (isIe) {
				window.history.pushState({}, '', href)
				setTimeout(() => {
					window.location.reload()
				}, 1)
			} else {
				if (mptRoute.includes(obj.frontUrl)) {
					location.replace(href)
				} else {
					window.history.pushState({}, '', href)
				}
			}
		} else {
			location.replace(obj.url);
		}
	}

	const renderPlugIframe = (url: string) => {
		// 有些第三方应用，需要获取Token
		if (url.includes('providerName')) {
			getToken(url.split('?')[1]).then((res) => {
				renderIframe(url, res.result)
			})
		} else {
			renderIframe(url)
		}
	};

	const handleRemoveIframe = (plugType: number = 1) => {
		const lastChild: any = (panshiAppDom || {}).lastChild;
		if (!lastChild) return;
		if (panshiAppDom && lastChild.tagName === 'IFRAME') {
			panshiAppDom.removeChild(lastChild);
			panshiAppDom.style.position = 'static';
		} else {
			// iframe使用定位的方式，固定在页面上， 父元素需要设置相对位置
			if (panshiAppDom && plugType === 2) panshiAppDom.style.position = 'relative';
		}
	};

	const renderIframe = (url: any, res?: any) => {
		console.log('renderIframe', panshiAppDom)
		const iframe = document.createElement('iframe');
		iframe.width = '100%';
		iframe.height = '100%';
		iframe.style.border = 'none';
		iframe.style.position = 'absolute';
		iframe.style.top = '0px';
		iframe.style.left = '0px';
		iframe.style.background = '#fff';
		iframe.style.zIndex = '1';
		if (res) {
			if (res.companyId) {
				iframe.src = `${url}&ticket=${res.ticket}&switchCompanyId=${res.companyId}`
			} else {
				iframe.src = `${url}&ticket=${res.ticket}`
			}
		} else {
			iframe.src = `${url}?&companyId=${currentUser.coId}&projectId=${currentUser.pjId}`
		}
		panshiAppDom && panshiAppDom.append(iframe)
	};

	return (
		<>
			<div className="wrapper">
				{
					isConfig
						? (
							<div className="wrapper-mask"></div>
						)
						: null
				}
				<div className="top">
					<div className="top_left">
						<div className="top_left_bottom">
							{
								monitors
									?
									(
										<iframe
											title="resg"
											src={src}
											style={{ width: '100%', border: '0px', height: '100%' }} />
									)
									:
									(
										<div className="Nopermission">
											<img src={Img6} className="img-bg" />
										</div>
									)
							}
							{monitors
								?
								(
									<div style={{ cursor: 'pointer' }} className="Notice">
										{currentUser.pjId
											? <a href={src} target="_blank">
												{/* 数智工地驾驶舱 */}
												{intl.formatMessage({ id: 'Digital_intelligence_construction_site_cockpit' })}
												<RightOutlined className="RightOutlined" />
											</a>
											:
											<a href={src} target="_blank">
												{/* 数智企业驾驶舱 */}
												{intl.formatMessage({ id: 'Digital_intelligence_enterprise_cockpit' })}
												<RightOutlined className="RightOutlined" />
											</a>}
									</div>
								)
								:
								<div className="isNotice">
									<span>
										{/* 暂未开通监控大屏应用 */}
										{intl.formatMessage({ id: 'SCREEN_NOT_OPEN' })}
									</span>
								</div>
							}
						</div>
					</div>
					<div className="top_right">
						<div
							className="subtop_right"
							onClick={() => { go((appList[0] || defaultAppList[0])) }}
						>
							<img src={Img3} className="img-bg" />
							{!loading && <Icon fileUuid={appList[0].bigWebLogo} obj={appList[0]} />}
							<div
								className="subtop_right_bottom"
							>
								<a>
									{(appList[0] || defaultAppList[0]).appName}
									{/* 应用一 */}
									{isConfig ? intl.formatMessage({ id: 'app1' }) : null}
								</a>
								<RightOutlined className="RightOutlined" />
							</div>
						</div>
						<div
							className="subtop_right"
							onClick={() => { go((appList[1] || defaultAppList[1])) }}
						>
							<img src={Img2} className="img-bg" />
							{!loading && <Icon fileUuid={appList[1].bigWebLogo} obj={appList[1]} />}
							<div
								className="subtop_right_bottom"
							>
								<a>
									{(appList[1] || defaultAppList[1]).appName}
									{isConfig ? intl.formatMessage({ id: 'app2' }) : null}
								</a>
								<RightOutlined className="RightOutlined" />
							</div>
						</div>
					</div>
				</div>
				<div className="bottom">
					<div
						className="Sub_bottom"
						onClick={() => { go((appList[4] || defaultAppList[4])) }}
					>
						<img src={Img1} className="img-bg" />
						{!loading && <Icon fileUuid={appList[4].bigWebLogo} obj={appList[4]} />}
						<div
							className="Sub_bottom_bottom"
						>
							<a>
								{(appList[4] || defaultAppList[4]).appName}
								{isConfig ? intl.formatMessage({ id: 'app5' }) : null}
							</a>
							<RightOutlined className="RightOutlined" />
						</div>
					</div>
					<div
						className="Sub_bottom"
						onClick={() => { go((appList[3] || defaultAppList[3])) }}
					>
						<img src={Img4} className="img-bg" />
						{!loading && <Icon fileUuid={appList[3].bigWebLogo} obj={appList[3]} />}
						<div
							className="Sub_bottom_bottom"
						>
							<a>
								{(appList[3] || defaultAppList[3]).appName}
								{isConfig ? intl.formatMessage({ id: 'app4' }) : null}
							</a>
							<RightOutlined className="RightOutlined" />
						</div>
					</div>
					<div
						className="Sub_bottom"
						onClick={() => { go((appList[2] || defaultAppList[2])) }}
					>
						<img src={Img5} className="img-bg" />
						{!loading && <Icon fileUuid={appList[2].bigWebLogo} obj={appList[2]} />}
						<div
							className="Sub_bottom_bottom"
						>
							<a>
								{(appList[2] || defaultAppList[2]).appName}
								{isConfig ? intl.formatMessage({ id: 'app3' }) : null}
							</a>
							<RightOutlined className="RightOutlined" />
						</div>
					</div>
				</div>
			</div>
		</>
	);
};

import Binding from '@/components/binding';
import { size, SizeEnum } from '@/panel/panel';
import { getCurrentUser, isPanelManger, revertDefault, savePanelList } from '@/services/api';
import { FormOutlined, PlusCircleOutlined } from '@ant-design/icons';
import {
  Button,
  Card,
  Divider,
  Empty,
  message,
  Modal,
  Radio,
  Space,
  Tooltip,
  Typography,
} from 'antd';
import arrayMove from 'array-move';
import React, { useEffect, useState } from 'react';
import { SortableContainer, SortableElement, SortEnd } from 'react-sortable-hoc';
import { useMount } from 'react-use';
import { useHistory, useIntl, useModel } from 'umi';
import Styles from './index.less';

enum PanelHeight {
  height = 374,
}
const { Text } = Typography;

const getContentHeight = () => {
  const { innerHeight } = window;
  return innerHeight - 56;
};
const RenderPanel = (props: any) => {
  const { payload } = props;
  const { panelList, setPanelList } = useModel('usePanel');
  const { panelNo, panelName, location, Component } = payload.value;
  const { isEdit, unUse } = payload;

  let height: number = PanelHeight.height;
  height = location.width === SizeEnum.FULLSCREEN ? getContentHeight() - 19 : PanelHeight.height;
  const width = location.width === SizeEnum.FULLSCREEN ? '100%' : `${size[location.width] * 100}%`;
  const [visible, setVisible] = useState(false);

  // 启用面版
  const onFinish = () => {
    if (!isEdit) return;
    setPanelList([
      ...panelList,
      {
        ...payload.value,
        location: payload.value.location,
      },
    ]);
  };
  return (
    <div key={panelNo} className={`${Styles.panelList}`} style={{ height, width }}>
      <Card
        onMouseEnter={(e) => {
          e.preventDefault();
          e.stopPropagation();
          if (unUse) return;
          setVisible(true);
        }}
        bodyStyle={{ height: '100%', display: 'flex', flexDirection: 'column' }}
        onMouseLeave={() => setVisible(false)}
        className={Styles.panelContent}
      >
        <div className={Styles.panelHeader}>{panelName}</div>
        <div className={Styles.panelBody}>{Component ? <Component {...payload.value} /> : ''}</div>
        {visible && isEdit ? (
          <div className={`${Styles.panelMask} panelEditMask`}>
            <div className="panelEditButton">
              <div>
                <span>宽：</span>
                <Radio.Group
                  onChange={({ target: { value } }) => {
                    setPanelList(
                      panelList.map((item: any) => {
                        return {
                          ...item,
                          location:
                            panelNo === item.panelNo
                              ? { ...item.location, width: value }
                              : item.location,
                        };
                      }),
                    );
                  }}
                  size="small"
                  value={location.width}
                  buttonStyle="solid"
                >
                  <Radio.Button value={SizeEnum.QUARTER} disabled={SizeEnum.QUARTER < location.min}>
                    1x
                  </Radio.Button>
                  <Radio.Button value={SizeEnum.THIRD} disabled={SizeEnum.THIRD < location.min}>
                    2x
                  </Radio.Button>
                  <Radio.Button value={SizeEnum.HALF} disabled={SizeEnum.HALF < location.min}>
                    2.5x
                  </Radio.Button>
                  <Radio.Button value={SizeEnum.TWOPART} disabled={SizeEnum.TWOPART < location.min}>
                    3x
                  </Radio.Button>
                  <Radio.Button
                    value={SizeEnum.FULLSCREEN}
                    disabled={SizeEnum.FULLSCREEN < location.min}
                  >
                    4x
                  </Radio.Button>
                </Radio.Group>
              </div>
              {panelNo !== 'panel_message_center' ? (
                <Button
                  onClick={() => {
                    const arr = panelList.filter((item: any) => item.panelNo !== panelNo);
                    setPanelList(arr);
                  }}
                  style={{ width: 195, marginTop: 15 }}
                  size="small"
                >
                  禁用
                </Button>
              ) : (
                ''
              )}
              <div style={{ fontSize: 12, textAlign: 'right', marginTop: 10 }}>提醒： 长按移动</div>
            </div>
          </div>
        ) : (
          ''
        )}
        {unUse ? (
          <div className={Styles.panelMask}>
            <div onClick={onFinish}>
              <PlusCircleOutlined />
              <span className={Styles.tip}>启用</span>
            </div>
          </div>
        ) : (
          ''
        )}
      </Card>
    </div>
  );
};

const SortableItem = SortableElement((payload: any) => {
  return <RenderPanel payload={payload} />;
});
const SortableList = SortableContainer((payload: any) => {
  const { items, isEdit } = payload;
  return (
    <div className={Styles.child}>
      {items.map((item: any, index: number) => (
        <SortableItem
          disabled={!isEdit}
          isEdit={isEdit}
          key={item.panelNo}
          index={index}
          value={item}
        />
      ))}
    </div>
  );
});

export default () => {
  const intl = useIntl();
  const { panelList, unUsePanelList, setPanelList, showEdit, getPanels, loading } = useModel(
    'usePanel',
  );
  const currentUser = getCurrentUser();
  const [isEdit, setIsEdit] = useState(false);
  const [perOrCom, setPerOrCom] = useState(1);
  const [modalVisible, setModalVisible] = useState(false);
  const [saveType, setSaveType] = useState(1);
  const [boxHeight, setBoxHeight] = useState(getContentHeight());
  const history = useHistory();
  useEffect(() => {
    const {
      query: { service, code },
    } = history.location || {};
    if (service) {
        /**如果页面携带外网跳转地址，则直接跳转至该外网页面 */
      if (service.includes('https')) {
        window.location.href = `${service}`;
      } else {
        /**如果页面携带跳转地址，则直接跳转至该页面 */
        window.location.href = `https://aq.cctc.cn${service}`;
      }
    } else {
      setBoxHeight(getContentHeight());
      window.addEventListener('resize', handleResize);
      return () => {
        window.removeEventListener('resize', handleResize);
      };
    }
  }, []);
  const handleResize = () => {
    setBoxHeight(getContentHeight());
  };
  const onSortEnd = (payload: SortEnd) => {
    const { oldIndex, newIndex } = payload;
    const arr = arrayMove(panelList, oldIndex, newIndex);
    setPanelList(arr);
  };
  const onFinishEdit = async () => {
    const isManger = await isPanelManger();
    if (isManger.result) {
      setSaveType(1);
      setModalVisible(true);
    } else {
      onFinishEditSave();
    }
  };
  const save = async () => {
    if (saveType === 1) {
      onFinishEditSave();
    } else {
      await revertDefault(perOrCom);
      getPanels();
      setPerOrCom(1);
      setIsEdit(false);
      setModalVisible(false);
      message.success('恢复成功');
    }
  };
  const onFinishRevertDefault = async () => {
    const isManger = await isPanelManger();
    if (isManger.result) {
      // 管理员
      setSaveType(2);
      setModalVisible(true);
    } else {
      await revertDefault(perOrCom);
      setIsEdit(false);
      getPanels();
      message.success('恢复成功');
    }
  };
  const onFinishEditSave = () => {
    savePanelList(
      panelList.map((item) => {
        return {
          panelId: item.panelId,
          location: JSON.stringify(item.location),
        };
      }),
      perOrCom,
    ).then(() => {
      setIsEdit(false);
      setPerOrCom(1);
      setModalVisible(false);
      message.success('保存成功');
    });
  };

  useMount(() => {
    getPanels();
  });

  return (
    <div className={Styles.panel} style={{ overflowY: 'scroll', height: boxHeight }}>
      <Binding />
      {isEdit ? (
        <div className={Styles.message}>
          {intl.formatMessage({ id: 'SETTING_PANEL' })}
          <Space>
            <Button
              onClick={() => {
                setIsEdit(false);
                getPanels();
              }}
            >
              {intl.formatMessage({ id: 'CANCEL' })}
            </Button>
            <Button
              className={Styles.revert}
              ghost={true}
              onClick={onFinishRevertDefault}
              type="primary"
            >
              {intl.formatMessage({ id: 'RESET' })}
            </Button>
            <Button onClick={onFinishEdit} type="primary">
              {intl.formatMessage({ id: 'SAVE' })}
            </Button>
          </Space>
        </div>
      ) : (
        ''
      )}

      {panelList.length === 0 && !loading && (
        <div className={Styles.empty}>
          <Empty
            description={<Text type="secondary">{intl.formatMessage({ id: 'NO_PANEL' })}</Text>}
          />
        </div>
      )}
      <SortableList
        isEdit={isEdit}
        items={panelList}
        axis="xy"
        helperClass="SortableContainer-moveImg"
        pressDelay={200}
        onSortEnd={onSortEnd}
      />

      {isEdit ? (
        <div>
          <Divider>未启用</Divider>
          <div style={{ display: 'flex', flexWrap: 'wrap' }}>
            {unUsePanelList.map((item) => {
              return (
                <RenderPanel
                  key={item.panelNo}
                  payload={{
                    value: item,
                    isEdit,
                    unUse: true,
                  }}
                />
              );
            })}
          </div>
        </div>
      ) : (
        ''
      )}
      {showEdit && !isEdit ? (
        <div className={Styles.edit} onClick={() => setIsEdit(!isEdit)}>
          <Tooltip title={`编辑模式`} placement="left">
            <FormOutlined />
          </Tooltip>
        </div>
      ) : (
        ''
      )}

      <Modal
        title="提示"
        visible={modalVisible}
        width={400}
        destroyOnClose={true}
        onCancel={() => {
          setModalVisible(false);
          setPerOrCom(1);
        }}
        footer={[
          <Button onClick={() => setModalVisible(false)}>取消</Button>,
          <Button key="submit" htmlType="submit" type="primary" onClick={() => save()}>
            确定
          </Button>,
        ]}
      >
        <>
          <p>当前面板设置应用于</p>
          <Radio.Group onChange={({ target: { value } }) => setPerOrCom(value)} value={perOrCom}>
            <Radio value={1}>个人</Radio>
            <Radio value={2}>{currentUser.pjId ? '项目所有人' : '企业所有人'}</Radio>
          </Radio.Group>
        </>
      </Modal>
    </div>
  );
};

@import "~@/primary.less";
.panel{
  width: 100%;
  height: 100%;
  background: @panelBackground-color;
  padding: 9px;
  position: relative;
  overflow: auto;
  overflow-x: hidden;
  .message{
    display: flex;
    padding: 0 7px 5px;
    justify-content: space-between;
    align-items: center;
    .revert{
      background: #fff !important;
    }
  }
  .empty{
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .child{
    width: 100%;
    // height: 100%;
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start;
    overflow: hidden;
    .empty{
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  .panelList{
    padding: 5px;
    position: relative;
    .panelContent{
      width: 100%;
      height: 100%;
      position: relative;
      .header{
        height: 30px;
        font-size: 16px;
        font-weight: bold;
      }
      .body{
        flex: 1;
        height: calc(100% - 25px);
      }
      .mask{
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.4);
        position: absolute;
        top: 0;
        left: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #fff;
        font-size: 18px;
        cursor: pointer;
        >div{
          .tip{
            margin-left: 10px;
          }
        }
      }
    }
  }
  :global{
    .ant-divider{
      &:before{
        border-top: 1px #93cee0  solid;
      }
      &:after{
        border-top: 1px #93cee0  solid;
      }
    }
  }
  .edit{
    position: fixed;
    right: -20px;
    bottom: 40px;
    width: 60px;
    height: 30px;
    background: #a9a9a9;
    border-radius: 20px 0 0 20px;
    cursor: pointer;
    display: flex;
    align-items: center;
    transition: right 0.5s;
    span{
      font-size: 16px;
      margin-left: 15px;
      color: #fff;
    }
    &:hover{
      right: 0;
      background: @primary-color;
    }
  }
  .messageMin{
    position: fixed;
    right: -20px;
    top: 80px;
    width: 60px;
    height: 30px;
    background: @primary-color;
    border-radius: 20px 0 0 20px;
    cursor: pointer;
    display: flex;
    align-items: center;
    transition: right 0.5s;
    .messageIcon{
      font-size: 16px;
      margin-left: 15px;
      color: #fff;
    }
    &:hover{
      right: 0
    }
  }
}
.panelEdit{
  >span{
    cursor: pointer;
    &:first-child:hover{
      color: @primary-color;
    }
    &:last-child:hover{
      color: red;
    }
  }
}

.panelHeader{
  height: 30px;
  font-size: 16px;
  font-weight: bold;
  margin-top: -7px;
}
.panelBody{
  flex: 1;
  height: 100%;
}
.panelMask{
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.4);
  position: absolute;
  z-index: 1;
  top: 0;
  left: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  font-size: 18px;
  >div{
    .tip{
      margin-left: 10px;
    }
  }
}
.header{
  border-bottom: 1px solid #f6f6f6;
  .reminding{
    color: @primary-color;
    &:hover{
      color: @primary-color;
    }
  }
  .myTodo{
    color: @primary-color;
    &:hover{
      color: @primary-color;
    }
  }
}
.ant-btn-text{
  border:0px;
  &:hover{
  background:rgba(0, 0, 0, 0);
  color: @primary-color;
  border:0px;
  }
  &:focus{
    background:rgba(0, 0, 0, 0);
    border:0px;
  }
}

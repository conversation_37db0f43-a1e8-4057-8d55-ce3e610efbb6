import server from '@/services/service';
import request from '@pms/react-utils/src/utils/request';
import CurrentUses from '@pms/react-current-user';
import fetchAntdCss from '@pms/react-utils/src/fetchCss';
import primary from '@pms/react-utils/src/primary';
import defaultSettings from '@/defaultSetting';
import {setLocale} from 'umi';

export async function render(oldRender: any) {
  if (!window.__POWERED_BY_QIANKUN__) {
    const current = new CurrentUses();
    await current.ssoFinished();
    oldRender()
  } else {
    oldRender()
  }
}

export const qiankun = {
  // 应用加载之前
  async bootstrap(props: any) {
    console.log('panel bootstrap');
		// setLocale('zh-TW', false);
  },
  // 应用 render 之前触发
  async mount(props: any) {
    if (props) {
      window.PMHOME = props.PMHOME;
      primary(props.PMHOME.setting.primary);
      setLocale(props.PMHOME.setting.locales,false);
    } else {
      fetchAntdCss();
      primary(defaultSettings.primary);
      window.PMHOME = {request, server, getCurrentUser: CurrentUses.getCurrentUser};
    }
    console.log('panel mount');
  },
  // 应用卸载之后触发
  async unmount(props: any) {
    console.log('panel unmount');
  },
};

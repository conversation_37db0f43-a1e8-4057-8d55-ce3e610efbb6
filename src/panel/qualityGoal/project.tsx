import React, { useEffect, useState } from 'react';
import styles from './index.less';

// assets
import { getGoalsApi } from './assets/api';

const Project = () => {
  const { getCurrentUser } = window.PMHOME;
  const currentUser = getCurrentUser();
  const [dataList, setDataList] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(false);

  // 列表
  const getGoals = () => {
    setLoading(true);
    getGoalsApi({
      companyId: currentUser.coId,
      departmentId: currentUser?.departmentId || undefined,
      goalType: 1,
      current: 1,
      size: 10,
    })
      .then((res: any) => {
        const { success, result = {} } = res || {};
        if (success) {
          const { records = [] } = result;
          setDataList(records);
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };
  useEffect(() => {
    getGoals();
  }, []);

  return (
    <div className={styles.project_content}>
      {dataList.map((el) => (
        <section className={styles.item}>
          <img
            src={require(`./assets/images/list_icon_${el?.contractStatus === 2 ? 'a' : 'h'}.png`)}
            alt=""
          />
          <span style={{ opacity: el?.contractStatus === 2 ? 0.85 : 0.45 }}>
            {el?.goalName || ''}
          </span>
        </section>
      ))}
    </div>
  );
};

export default Project;

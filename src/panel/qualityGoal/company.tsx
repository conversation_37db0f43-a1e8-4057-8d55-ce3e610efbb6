import { Bar, Bullet } from '@ant-design/plots';
import { Radio, Space, Spin, Tabs } from 'antd';
import React, { useEffect, useMemo, useState } from 'react';
import styles from './index.less';

// assets
import { getGoalCompletionAnalyzeApi, getStatisticsOverviewApi } from './assets/api';
import { headerConfigs } from './assets/configs';
import { createBulletConfigs, createGroupBarConfigs } from './assets/utils';

const Company = () => {
  const [info, setInfo] = useState<any>({});

  const [params, setParams] = useState<{
    goalClassify: string;
    sort: number;
    orgType: number;
  }>({
    goalClassify: '1',
    sort: 1,
    orgType: 1,
  });
  const [dataList, setDataList] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(false);

  // 总览
  const getStatisticsOverview = () => {
    getStatisticsOverviewApi({}).then((res: any) => {
      const { success, result } = res || {};
      if (success) {
        setInfo(result);
      }
    });
  };
  useEffect(() => {
    getStatisticsOverview();
  }, []);

  // 获取图表数据
  const getGoalCompletionAnalyze = () => {
    setLoading(true);
    getGoalCompletionAnalyzeApi({
      ...params,
      goalClassify: Number(params.goalClassify),
    })
      .then((res: any) => {
        const { success, result } = res;
        if (success) {
          setDataList(result);
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };
  useEffect(() => {
    getGoalCompletionAnalyze();
  }, [params]);

  // 获取覆盖率
  const getRate = (count: number) => {
    if (info?.projectCount) {
      return ((count / info.projectCount) * 100).toFixed(2);
    }
    return 0;
  };

  const onChangeSort = (e: any) => {
    const value = e.target.value;
    setParams((prev) => ({
      ...prev,
      sort: value,
    }));
  };

  const onChangeOrgType = (e: any) => {
    const value = e.target.value;
    setParams((prev) => ({
      ...prev,
      orgType: value,
    }));
  };

  const onChangeGoalClassify = (value: any) => {
    setParams((prev) => ({
      ...prev,
      goalClassify: value,
    }));
  };

  const renderStatistics = () => (
    <section className={styles.statistics}>
      {headerConfigs.map((el) => (
        <section key={el.key} className={styles.item}>
          <h6>
            <span>{el.label}</span>
          </h6>
          <aside>
            <span className={styles.count}>
              {el.label === '项目总数' ? info?.[el.key] || 0 : getRate(info?.[el.key] || 0)}
            </span>
            <span>{el.unit}</span>
          </aside>
        </section>
      ))}
    </section>
  );
  const renderForm = () => (
    <section className={styles.title}>
      <span className={styles.text}>{params.orgType === 1 ? '企业' : '项目'}目标完成率排行</span>
      <Space>
        <Radio.Group value={params.sort} onChange={onChangeSort}>
          <Radio.Button value={1}>正序</Radio.Button>
          <Radio.Button value={2}>倒序</Radio.Button>
        </Radio.Group>
        <Radio.Group value={params.orgType} onChange={onChangeOrgType}>
          <Radio.Button value={1}>按组织</Radio.Button>
          <Radio.Button value={2}>按项目</Radio.Button>
        </Radio.Group>
      </Space>
    </section>
  );
  const renderMain = () => (
    <section>
      <Tabs activeKey={params.goalClassify} onChange={onChangeGoalClassify}>
        <Tabs.TabPane tab="合同目标" key="1">
          {useMemo(() => {
            if (loading) {
              return (
                <section style={{ textAlign: 'center' }}>
                  <Spin />
                </section>
              );
            } else {
              return (
                <section style={{ overflowY: 'auto', height: 135 }}>
                  <Bullet
                    key="Bullet"
                    style={{ height: dataList.length * 50 }}
                    {...createBulletConfigs(dataList)}
                  />
                </section>
              );
            }
          }, [dataList, loading])}
        </Tabs.TabPane>
        <Tabs.TabPane tab="争创目标" key="2">
          {useMemo(() => {
            if (loading) {
              return (
                <section style={{ textAlign: 'center' }}>
                  <Spin />
                </section>
              );
            } else {
              return (
                <Bar
                  key="Bar"
                  style={{ height: 130 }}
                  {...createGroupBarConfigs(dataList.reverse())}
                />
              );
            }
          }, [loading, dataList])}
        </Tabs.TabPane>
      </Tabs>
    </section>
  );

  return (
    <div>
      {renderStatistics()}
      {renderForm()}
      {renderMain()}
    </div>
  );
};

export default Company;

// 创建分组子弹图
export const createBulletConfigs: any = (data: any[]) => {
  const _data = data.reverse().map((el) => ({
    title: el.name,
    ranges: [60, 90, 100],
    value: el?.completeRate ? el.completeRate * 100 : 0,
    target: 100,
  }));

  return {
    data: _data,
    measureField: 'value',
    rangeField: 'ranges',
    targetField: 'target',
    xField: 'title',
    color: {
      range: ['#FFbcb8', '#FFe0b0', '#bfeec8'],
      value: '#5B8FF9',
      target: '#39a3f4',
    },
    label: {
      measure: {
        position: 'middle',
        style: {
          fill: '#fff',
        },
      },
    },
    meta: {
      value: {
        formatter: (v: any) => {
          return (v ? v : 0) + '%';
        },
      },
      title: {
        formatter: (v: any) => {
          return v && v.length > 5 ? v.slice(0, 8) + '...' : v;
        },
      },
    },
    tooltip: {
      customContent: (title: any) => {
        const {
          name = '',
          completeRate = 0,
          completeCount = 0,
          incompleteCount = 0,
        }: any = data.find((el) => el.name === title) || {};
        return `<div style="padding: 12px"> 
          <h5>${name}</h5>
          <h5>完成数： ${completeCount}</h5>
          <h5>未完成数： ${incompleteCount}</h5>
          <h5>完成率:  ${completeRate ? (completeRate * 100).toFixed(2) : 0}%</h5>
        </div>`;
      },
    },
    xAxis: {
      line: null,
    },
    yAxis: false,
    scrollbar: {
      type: 'vertical',
    },
    // 自定义 legend
    legend: {
      custom: true,
      position: 'top',
      items: [
        {
          value: '差',
          name: '差',
          marker: {
            symbol: 'square',
            style: {
              fill: '#FFbcb8',
              r: 5,
            },
          },
        },
        {
          value: '良',
          name: '良',
          marker: {
            symbol: 'square',
            style: {
              fill: '#FFe0b0',
              r: 5,
            },
          },
        },
        {
          value: '优',
          name: '优',
          marker: {
            symbol: 'square',
            style: {
              fill: '#bfeec8',
              r: 5,
            },
          },
        },
        {
          value: '实际值',
          name: '实际值',
          marker: {
            symbol: 'square',
            style: {
              fill: '#5B8FF9',
              r: 5,
            },
          },
        },
        {
          value: '目标值',
          name: '目标值',
          marker: {
            symbol: 'line',
            style: {
              stroke: '#39a3f4',
              r: 5,
            },
          },
        },
      ],
    },
    // interactions: [
    //   {
    //     type: 'tooltip',
    //     cfg: { start: [{ trigger: 'element:click' }] }
    //   }
    // ]
  };
};

// 创建分组条形图
export const createGroupBarConfigs: any = (data: any[]) => {
  let _data: any[] = [];
  for (let item of data) {
    _data = _data.concat([
      {
        label: item.name,
        type: '完成率',
        value: item.completeRate ? item.completeRate * 100 : 0,
      },
    ]);
  }
  return {
    data: _data,
    isGroup: true,
    xField: 'value',
    yField: 'label',
    seriesField: 'type',
    marginRatio: 0,
    color: ['#FFCD00', '#2478FF'],
    label: {
      // 可手动配置 label 数据标签位置
      position: 'right', // 'left', 'middle', 'right'
      offset: 4,
    },
    legend: {
      position: 'top',
    },
    scrollbar: {
      type: 'vertical',
    },
    barStyle: {
      radius: [2, 2, 0, 0],
    },
    meta: {
      value: {
        formatter: (v: any) => `${(v * 1).toFixed(2)}%`,
      },
      label: {
        formatter: (v: any) => {
          return v && v.length > 5 ? v.slice(0, 5) + '...' : v;
        },
      },
    },
    // interactions: [
    //   {
    //     type: 'tooltip',
    //     cfg: { start: [{ trigger: 'element:click' }] }
    //   }
    // ]
  };
};

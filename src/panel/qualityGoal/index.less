.statistics {
  display: flex;
  .item {
    flex: 1;
    font-size: 14px;
    text-align: center;
    .count {
      margin-right: 8px;
      font-weight: 500;
      font-size: 24px;
    }
  }
}

.title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 10px 0;
  .text {
    position: relative;
    padding-left: 12px;
    &:before {
      position: absolute;
      top: 0;
      left: 0;
      width: 4px;
      height: 22px;
      background-color: #2478ff;
      content: '';
    }
  }
}

.project_content {
  height: 280px;
  margin-top: 20px;
  overflow-y: auto;
  .item {
    height: 64px;
    margin-bottom: 12px;
    padding: 12px 16px;
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 7px 13px -3px #0000001a;
    img {
      width: 50px;
      height: 50px;
    }
    span {
      margin-left: 12px;
    }
  }
}

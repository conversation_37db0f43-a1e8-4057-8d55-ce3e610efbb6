import { Empty, Layout, Radio } from 'antd';
import React, { useState, useMemo } from 'react';
import { axis, grid } from '@/panel/echartOptions';
import { useRequest } from 'ahooks';
import More from '@/components/more/index';
import ReactEcharts from 'echarts-for-react';
import { getDataZoom } from './slider';
import { getJcfglph } from '@/services/risk';
import { getCurrentUser } from '@/services/api';

const { Content } = Layout;

const HazardAnalyse = () => {
  const [dataSource, setDataSource] = useState([]);
  const currentUser = getCurrentUser();

  const { data } = useRequest(async () => {
    const res: any = await getJcfglph({ companyId: currentUser.coId });
    setDataSource(res.data);
  });

  const config = useMemo(() => {
    return {
      grid,
      tooltip: {
        trigger: 'axis',
        formatter: function (params) {
          var relVal = params[0].name;
          for (var i = 0; i < params.length; i++) {
            relVal +=
              '<br/>' +
              params[i].marker +
              params[i].seriesName +
              ' : ' +
              params[i].value +
              (i === 0 ? '个' : '%');
          }
          return relVal;
        },
      },
      legend: {
        data: ['检查项目数', '检查覆盖率'],
        itemWidth: 10,
        itemHeight: 10,
      },
      xAxis: {
        type: 'category',
        show: true,
        interval: 0,
        ...axis,
        data: dataSource?.map((x) => x.department_name),
      },
      yAxis: [
        {
          type: 'value',
          name: '数量/个',
          ...axis,
          nameTextStyle: {
            color: '#666666',
          },
        },
        {
          type: 'value',
          name: '覆盖率/%',
          ...axis,
          nameTextStyle: {
            color: '#666666',
          },
        },
      ],
      series: [
        {
          name: '检查项目数',
          data: dataSource?.map((x) => x.check_project_num),
          type: 'bar',
          barMinWidth: 14,
          barMaxWidth: 14,
          color: '#2478FF',
        },
        {
          name: '检查覆盖率',
          data: dataSource?.map((x) => x.cover_project_rate),
          type: 'line',
          barMinWidth: 14,
          barMaxWidth: 14,
          yAxisIndex: 1,
          color: '#FFCD00',
        },
      ],
      dataZoom: getDataZoom(
        {
          sliderSize: 8,
          isSliderFromEnd: false,
        },
        dataSource.length,
      ),
    };
  }, [dataSource]);

  function jumpPage(url: string) {
    localStorage.setItem('checkItem', '1');
    window.location.replace(url);
  }

  return (
    <Layout className="layoutPanelPm">
      <Content style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
        {/* <More jumpUrl="/console/inspect/#/safety" onClick={jumpPage} /> */}
        {dataSource?.length ? (
          <ReactEcharts style={{ width: '100%', height: 300 }} option={config} />
        ) : (
          <Empty />
        )}
      </Content>
    </Layout>
  );
};

export default HazardAnalyse;

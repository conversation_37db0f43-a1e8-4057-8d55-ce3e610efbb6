/**
 * echarts 滚动条
 *
 * <AUTHOR>
 * @date 2024/4/10
 */

import { EChartOption } from 'echarts';

// 滚动条 interface
export interface SliderConfig {
  // 滚动条显示数据个数（设置非0值会 ---> 自动开启滚动条）
  sliderSize?: number;
  // 滚动条初始化在末尾位置
  isSliderFromEnd?: boolean;
}

// 获取滚动条配置
export function getDataZoom(
  props: SliderConfig,
  total: number,
): EChartOption['dataZoom'] | undefined {
  if (props?.sliderSize && total && props?.sliderSize < total) {
    const addition = props?.sliderSize - 1;
    const start = props?.isSliderFromEnd ? total - addition : 0;
    const end = start + addition;
    return [
      {
        type: 'slider',
        orient: 'horizontal',
        show: true, //控制滚动条显示隐藏
        realtime: true, //拖动滚动条时是否动态的更新图表数据
        // @ts-ignore
        height: 8, //滚动条高度
        startValue: start,
        endValue: end,
        bottom: 4,
        dataBackground: {
          lineStyle: {
            color: 'transparent',
          },
          areaStyle: {
            color: 'transparent',
          },
        },
        handleSize: 10,
        handleStyle: {
          color: '#E5E5E5',
        },
        borderColor: 'transparent',
        textStyle: {
          color: 'transparent',
        },
        fillerColor: '#E5E5E5',
      },
    ];
  }
}

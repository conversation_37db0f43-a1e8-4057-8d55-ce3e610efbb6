import React, { useState, useEffect } from "react";
import { Layout, Radio, Space, Row, Col, Divider, Progress, message } from 'antd';
import ReactEcharts from 'echarts-for-react';
import DescriptionsCard from '@/components/DescriptionsCard';
import opts, { axis, title, legend, color, grid } from '@/panel/echartOptions';
import { getEpidemicManageApi } from '@/services/epidemic';
import { useMount } from 'react-use';
import More from "@/components/more";
import Styles from './index.less'
import { getCurrentUser } from '@/services/api';
import defaultSetting from "@/defaultSetting";
import PercentPie from './components/PercentPie/index';
import defaultOptions from './defaultEchartOption'
const { Header, Content } = Layout;

interface echartData {
  name: string,
  maxValue: number,
  value: number,
  percent: string
}
//防疫管理面板
export default () => {
  const currentUser = getCurrentUser();
  const optionsDate = [
    { label: '全部', value: 0 },
    { label: '正式人员', value: 1 },
    { label: '临时人员', value: 2 }
  ];
  const [dateType, setDateType] = useState(0);
  const [travelCard, setTravelCard] = useState<TravelProps>({})
  const [nucleicTest, setNucleicTest] = useState<NucleicTestProps>({})
  const [vaccine, setVaccine] = useState<Vaccine>({})
  const [titleData, settitleData] = useState<any>([{ name: '行程卡', percent: '有无中高风险地区旅居史' }, { name: '核酸检测', percent: '' }, { name: '疫苗接种', percent: '' }]);
  const [panelData, setpanelData] = useState({})
  useMount(() => {
    getEpidemicManage()
  })
  useEffect(() => {
    getEpidemicManage()

  }, [dateType])
  const getEpidemicManage = () => {
    getEpidemicManageApi(dateType).then(res => {
      if (res.errorMsg) {
        message.error(res.message || '查询失败')
        return
      }
      if (res && res.result) {
        const qTravel = res.result.travel || {}
        const qNuclein = res.result.nuclein || {}
        const qVaccine = res.result.vaccine || {}
        const bePresent = getNumber(res.result.countPresent)
        setTravelCard({
          bePresent, // 在场人员数目
          report: getNumber(qTravel.reportNum),    // 上报数目
          reportRate: getRateNumber(qTravel.reportProportion),    // 上报数目 - 占比
          existPeople: getNumber(qTravel.existNum), // 有人
          existPeopleRate: getRateNumber(qTravel.existProportion), // 有人 - 占比
          leavePeople: getNumber(qTravel.noneNum), // 无人
          leavePeopleRate: getRateNumber(qTravel.noneProportion) // 无人 - 占比
        })
        setNucleicTest({
          bePresent, // 在场人员数目
          report: getNumber(qNuclein.reportNum),    // 上报数目
          reportRate: getRateNumber(qNuclein.reportProportion),    // 上报数目 - 占比
          YinPeople: getNumber(qNuclein.negativeNum), // 阴性
          YinPeopleRate: getRateNumber(qNuclein.negativeProportion), // 阴性 - 占比
          YangPeople: getNumber(qNuclein.positiveNum), // 阳性
          YangPeopleRate: getRateNumber(qNuclein.positiveProportion) // 阳性 - 占比
        })
        setVaccine({
          bePresent, // 在场人员数目
          report: getNumber(qVaccine.reportNum),    // 上报数目
          reportRate: getRateNumber(qVaccine.reportProportion),    // 上报数目 - 占比
          stitch1People: getNumber(qVaccine.vaccine1Num), // 接种1针
          stitch1PeopleRate: getRateNumber(qVaccine.vaccine1Proportion), // 接种1针 - 占比
          stitch2People: getNumber(qVaccine.vaccine2Num), // 接种2针
          stitch2PeopleRate: getRateNumber(qVaccine.vaccine2Proportion), // 接种2针 - 占比
          stitch3People: getNumber(qVaccine.vaccine3Num), // 接种3针
          stitch3PeopleRate: getRateNumber(qVaccine.vaccine3Proportion), // 接种3针 - 占比
          stitch4People: getNumber(qVaccine.vaccine4Num), // 接种加强针
          stitch4PeopleRate: getRateNumber(qVaccine.vaccine4Proportion) // 接种加强针 - 占比
        })
      }
    });
  }
  const getNumber = (num?: number) => num === undefined ? 0 : num // 0
  // 解决乘法精度丢失问题
  // 例如： 0.0167 => 1.67
  const getRateNumber = (num?: number) => {

    return parseFloat(String(num)) === 0 ? '0' : (num * 100).toFixed(1)
  }
  return (
    <Layout className='layoutPanelPm' style={{ height: 490 }}>
      <More jumpUrl={currentUser.type === 1 ? `` : `${window.location.origin}/console/lw/#/service_subcontract`} />
      <Header className={Styles.headTitle}>
        <Radio.Group
          size='small'
          options={optionsDate}
          onChange={({ target: { value } }) => setDateType(value)}
          defaultValue={dateType}
          optionType="button"
          buttonStyle="solid"
          className={Styles.radioGroup}
        />
      </Header>

      <div>
        <div className={`${Styles.contentTitle} ${Styles.mt15}`}>
          <span>{titleData[0].name}</span>
          <span>{titleData[0].percent}</span>
        </div>
        <Row>
          <Col span="9" >
            <PercentPie
              personCounts={getNumber(travelCard.report)}
              percent={getNumber(travelCard.reportRate)}
              bePresentCounts={getNumber(travelCard?.bePresent)}
              title='在场(人)'
              width='100%'
              height='100%'
              fontSize={14}
            />
          </Col>
          <Col span="12" style={{ height: 90 }} offset={3}>
            <div className={Styles.lineBodyRight}>
              <div className={Styles.tipTop}>
                <div className={Styles.tipTopLeft}>无（人）</div>
                <div className={Styles.tipTopRight}>有（人）</div>
              </div>
              <Progress
                showInfo={false}
                percent={travelCard.leavePeopleRate}
                strokeColor='#3996ff'
                trailColor={(Number(travelCard.leavePeopleRate)===0&&Number(travelCard.existPeopleRate)===0)?'#3996ff':'#ffa200'}
                strokeLinecap='square'
              />
              <div className={Styles.tipBottom}>
                <div className={Styles.tipBottomLeft}>{getNumber(travelCard.leavePeople)}/{getNumber(travelCard.leavePeopleRate)}%</div>
                <div className={Styles.tipBottomRight}>{getNumber(travelCard.existPeople)}/{getNumber(travelCard.existPeopleRate)}%</div>
              </div>
            </div>
          </Col>
        </Row>
        <Row className={Styles.parentItem}>
          <span className={Styles.positionTips}>{titleData[1].name}</span>
          <Col span="9" >
            <PercentPie
              personCounts={getNumber(nucleicTest.report)}
              percent={getNumber(nucleicTest.reportRate)}
              bePresentCounts={getNumber(nucleicTest?.bePresent)}
              title='在场(人)'
              width='100%'
              height='100%'
              fontSize={14}
            />
          </Col>
          <Col span="12" style={{ height: 90 }} offset={3}>
            <div className={Styles.lineBodyRight}>
              <div className={Styles.tipTop}>
                <div className={Styles.tipTopLeft}>阴性（人）</div>
                <div className={Styles.tipTopRight}>阳性（人）</div>
              </div>
              <Progress
                showInfo={false}
                percent={nucleicTest.YinPeopleRate}
                strokeColor='#3996ff'
                trailColor={(Number(nucleicTest.YinPeopleRate)===0&&Number(nucleicTest.YangPeopleRate)===0)?'#3996ff':'#ffa200'}
                strokeLinecap='square'
              />
              <div className={Styles.tipBottom}>
                <div className={Styles.tipBottomLeft}>{getNumber(nucleicTest.YinPeople)}/{getNumber(nucleicTest.YinPeopleRate)}%</div>
                <div className={Styles.tipBottomRight}>{getNumber(nucleicTest.YangPeople)}/{getNumber(nucleicTest.YangPeopleRate)}%</div>
              </div>
            </div>
          </Col>
        </Row>
        <Row className={Styles.parentItem}>
          <span className={Styles.positionTips}>{titleData[2].name}</span>
          <Col span="9" >
            <PercentPie
              personCounts={getNumber(vaccine.report)}
              percent={getNumber(vaccine.reportRate)}
              bePresentCounts={getNumber(vaccine?.bePresent)}
              title='在场(人)'
              width='100%'
              height='100%'
              fontSize={14}
            />
          </Col>
          <Col span="12" offset={3} justify="end">
            <div className={Styles.blockList}>
              <div className={`${Styles.block}`}>
                <div className={`${Styles.blockTop}`}>{getNumber(vaccine.stitch1People)}/{getNumber(vaccine.stitch1PeopleRate)}%</div>
                <div className={`${Styles.blockBottom}`}>接种1针(人)</div>
              </div>
              <div className={`${Styles.block} ${Styles.textRight}`}>
                <div className={`${Styles.blockTop}`}>{getNumber(vaccine.stitch2People)}/{getNumber(vaccine.stitch2PeopleRate)}%</div>
                <div className={`${Styles.blockBottom}`}>接种2针(人)</div>
              </div>
              <div className={`${Styles.block}`}>
                <div className={`${Styles.blockTop}`}>{getNumber(vaccine.stitch3People)}/{getNumber(vaccine.stitch3PeopleRate)}%</div>
                <div className={`${Styles.blockBottom}`}>接种3针(人)</div>
              </div>
              <div className={`${Styles.block} ${Styles.textRight}`}>
                <div className={`${Styles.blockTop}`}>{getNumber(vaccine.stitch4People)}/{getNumber(vaccine.stitch4PeopleRate)}%</div>
                <div className={`${Styles.blockBottom}`}>接种加强针(人)</div>
              </div>
            </div>
          </Col>
        </Row>
      </div>


      <Content>

      </Content>
    </Layout>
  )
}

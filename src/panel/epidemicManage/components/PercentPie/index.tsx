import React, {useEffect, useState} from 'react';
import ReactEcharts from 'echarts-for-react';
import opts from '@/echart';

/**
 * 环形图 （中间是文字）
 * @param props
 */
export default (props: {
  title: string;              // 圆环内文字
  personCounts: number;       // 人员数目
  percent: number;            // 比例
  bePresentCounts: number;    // 在场人数
  width: number | string;
  height: number | string;
  fontSize: number;
}) => {
  const {
    percent = 0,
    personCounts = 0,
    bePresentCounts = 0,
    width = '100%',
    height = '100%',
    fontSize,
    title
  } = props;
  const [echartsOption, setEchartOption] = useState({} as any);

  const outerRadius = 80
  const innerRadius = outerRadius - 15

  useEffect(() => {
    setSeries(percent)
  }, [bePresentCounts, percent, title])

  function getCountsLine (counts) {
    const str = `上报${counts}人`
    return str
  }


  const setSeries = (percent = 0) => {
    setEchartOption({
      tooltip: {
        show: false,
      },
      title: {
        text: `${bePresentCounts || '0'}`,
        subtext: title || '标题',
        y: '26%',
        x: '48%',
        textAlign: 'center',
        textStyle:{
          color: '#444444',
          fontSize: 16,
          // fontSize: fontSize * 1.5,
          fontWeight: 'bold',
          // lineHeight: 15
        },
        subtextStyle:{
          color:'#666666',
          fontSize: 12,
        }
      },
      series: [
        {
          type: 'pie',
          radius: [`${innerRadius}%`, `${outerRadius}%`],
          center: ['50%', '50%'],
          hoverAnimation:false,
          label: {
            normal: {
              show: true,
              formatter: `\n\n${getCountsLine(personCounts)}\n${percent}%`,
              fontWeight: 'bold',
              fontSize,
              lineStyle: {
                color: '#14BD85',
                width: 2
              }
            },
            emphasis: {
              show: true
            }
          },
          labelLine: {
            normal: {
              "show": true,
              "smooth": false,
              "length": 10,
              "length2": 10
            },
            emphasis: {
              "show": true
            }
          },
          data: [
            {
              name: '11',
              value: percent,
              itemStyle: {
                color: '#14BD85'
              },
              // label: {
              //   show: true
              // }
            },
            {
              name:'22',
              value: 100 - percent,
              itemStyle: {
                color: "transparent"
              },
            }
          ]
        },
        {
          type: 'pie',
          label: {
            normal: {
              show : false
            }
          },
          radius: [`${innerRadius}%`, `${outerRadius - 3}%`],
          center: ['50%', '50%'],
          hoverAnimation:false,
          data: [
            {
              value: percent,
              itemStyle: {
                color: '#14BD85'
              }
            },
            {
              value: 100 - percent,
              itemStyle: {
                color:'#e6ebf5'
              },
              label: {
                show:false
              }
            }
          ]
        }
      ],
    })
  }

  return (
    <ReactEcharts
      opts={opts}
      style={{width, height}}
      option={echartsOption}
      theme="clear"
    />
  )
}

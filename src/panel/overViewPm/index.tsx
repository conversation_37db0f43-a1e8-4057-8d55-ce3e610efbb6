import React, { useState } from 'react';
import { Layout, Descriptions } from 'antd';
import { useRequest } from 'ahooks';
import More from '@/components/more/index';
import { getGgXmxx } from '@/services/risk';
import { getCurrentUser } from '@/services/api';
import styles from './index.less';

const { Content } = Layout;
const overViewPm = () => {
  const currentUser = getCurrentUser();

  const { data: { project_name, address, project_type_name, construct_scale } = {} } = useRequest(
    async () => {
      const { projectId } = currentUser;
      const res: any = await getGgXmxx({ projectId });
      return res.data;
    },
  );

  function jumpPage(url: string) {
    localStorage.setItem('checkItem', '1');
    window.location.replace(url);
  }

  return (
    <Layout className="layoutPanelPm">
      <Content style={{ display: 'flex' }}>
        {/* <More jumpUrl="/console/inspect/#/safety" onClick={jumpPage} /> */}
        <div className={styles.content_style}>
          <div className={styles.content_item_style}>
            <span className={styles.item_left_style}>项目名称</span>
            <span className={styles.item_right_style}>{project_name}</span>
          </div>
          <div className={styles.content_item_style}>
            <span className={styles.item_left_style}>项目地址</span>
            <span className={styles.item_right_style}>{address}</span>
          </div>
          <div className={styles.content_item_style}>
            <span className={styles.item_left_style}>项目类型</span>
            <span className={styles.item_right_style}>{project_type_name}</span>
          </div>
          <div className={styles.content_item_style}>
            <span className={styles.item_left_style}>项目规模</span>
            <span className={styles.item_right_style}>{construct_scale}</span>
          </div>
        </div>
      </Content>
    </Layout>
  );
};

export default overViewPm;

.headTitle {
  position: relative;

  .radioGroup {
    position: absolute;
    right: 50px;
    top: -28px;
  }
}
.mt15{
  margin-top: 15px;
}
li {
  list-style: none;
}

.contentTitle {
  display: flex;
  padding-bottom: 4px;
  justify-content: space-between;
  // margin-left: -12px;
  span {
    font-size: 16px;
    font-family: PingFangSC, PingFangSC-Medium;
    font-weight: 500;
    text-align: left;
    color: #191919;
    line-height: 22px;
  }
}
.lineBodyRight {
  flex: 1;
  //background: blue;
  :global {
    .ant-progress-inner {
      position: relative;
      display: inline-block;
      width: 100%;
      overflow: hidden;
      vertical-align: middle;
      background-color: #ffa200;
      border-radius: 100px;
    }
  }
}
.tipTop {
  font-size: 14px;
  display: flex;

  .tipTopLeft {
    flex: 1;
  }
  .tipTopRight {
    flex: 1;
    text-align: right;
  }
}
.tipBottom {
  display: flex;
  font-size: 18px;

  .tipBottomLeft {
    flex: 1;
    color: #3996ff;
  }
  .tipBottomRight {
    flex: 1;
    text-align: right;
    color: #ffa200;
  }
}
.blockList {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  margin-top: -10px;

  .block {
    width: 50%;
    height: 50%;
    display: flex;
    //text-align: center;
    flex-direction: column;
    //background: red;

    .blockTop {
      flex: 1;
      font-size: 20px;
      font-weight: 500;
      color: #333333;
    }

    .blockBottom {
      flex: 1;
      font-size: 14px;
      color: #666666;
    }
  }
}
.textRight {
  text-align: right;
}
.parentItem{
  position: relative;
}
.positionTips{
  position: absolute;
  font-size: 16px;
  font-family: PingFangSC, PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  color: #191919;
  line-height: 22px;
  left: 2px;
  top: -12px;
}
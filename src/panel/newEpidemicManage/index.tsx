import React, { useState, useEffect } from "react";
import { Layout, Radio, Space, Row, Col, Divider, Progress, message } from 'antd';
import { getHomepageOverview } from '@/services/epidemic';
import { useMount } from 'react-use';
import More from "@/components/more";
import Styles from './index.less'
import { getCurrentUser } from '@/services/api';
import PercentPie from './components/PercentPie/index';
const { Header, Content } = Layout;

interface echartData {
  name: string,
  maxValue: number,
  value: number,
  percent: string
}
//防疫管理面板
export default () => {
  const currentUser = getCurrentUser();
  const optionsDate = [
    // { label: '全部', value: 0 },
    { label: '正式人员', value: 1 },
    { label: '临时人员', value: 2 }
  ];
  const [dateType, setDateType] = useState(1);
  const [travelCard, setTravelCard] = useState<TravelProps>({})
  const [nucleicTest, setNucleicTest] = useState<NucleicTestProps>({})
  const [vaccine, setVaccine] = useState<Vaccine>({})
  const [titleData, settitleData] = useState<any>([{ name: '行程卡', percent: '有无中高风险地区旅居史' }, { name: '核酸检测', percent: '' }, { name: '疫苗接种', percent: '' }]);
  const [panelData, setpanelData] = useState({})
  useMount(() => {
    getEpidemicManage()
  })
  useEffect(() => {
    getEpidemicManage()

  }, [dateType])
  const getEpidemicManage = () => {
    getHomepageOverview(dateType).then(res => {
      if (res.errorMsg) {
        message.error(res.message || '查询失败')
        return
      }
      if (res && res.result) {
        const qTravel = res.result.travel || { presenceCnt: 0, reportCnt: 0, reportRate: 0 }
        const qNuclein = res.result.nuclein || { presenceCnt: 0, reportCnt: 0, reportRate: 0 }
        const nucleinNegative = res.result.nucleinNegative || { presenceCnt: 0, reportCnt: 0, reportRate: 0 }
        const nucleinPositive = res.result.nucleinPositive || { presenceCnt: 0, reportCnt: 0, reportRate: 0 }
        const firstVaccines = res.result.firstVaccines || { presenceCnt: 0, reportCnt: 0, reportRate: 0 }
        const secondVaccines = res.result.secondVaccines || { presenceCnt: 0, reportCnt: 0, reportRate: 0 }
        const thirdVaccines = res.result.thirdVaccines || { presenceCnt: 0, reportCnt: 0, reportRate: 0 }
        const fourthVaccines = res.result.fourthVaccines || { presenceCnt: 0, reportCnt: 0, reportRate: 0 }
        const qVaccine = res.result.vaccines || { presenceCnt: 0, reportCnt: 0, reportRate: 0 }
        const bePresent = getNumber(res.result.travel?.presenceCnt ? res.result.travel.presenceCnt : 0)
        setTravelCard({
          bePresent, // 在场人员数目
          report: getNumber(qTravel.reportCnt),    // 上报数目
          reportRate: qTravel.reportRate,    // 上报数目 - 占比
          // existPeople: getNumber(qTravel.existNum), // 有人
          // existPeopleRate: getRateNumber(qTravel.existProportion), // 有人 - 占比
          // leavePeople: getNumber(qTravel.noneNum), // 无人
          // leavePeopleRate: getRateNumber(qTravel.noneProportion) // 无人 - 占比
        })
        setNucleicTest({
          bePresent, // 在场人员数目
          report: getNumber(qNuclein.reportCnt),    // 上报数目
          reportRate: qNuclein.reportRate,    // 上报数目 - 占比
          YinPeople: getNumber(nucleinNegative.reportCnt), // 阴性
          YinPeopleRate: nucleinNegative.reportRate, // 阴性 - 占比
          YangPeople: getNumber(nucleinPositive.reportCnt), // 阳性
          YangPeopleRate: nucleinPositive.reportRate // 阳性 - 占比
        })
        setVaccine({
          bePresent, // 在场人员数目
          report: getNumber(qVaccine.reportCnt),    // 上报数目
          reportRate: qVaccine.reportRate,    // 上报数目 - 占比
          stitch1People: getNumber(firstVaccines.reportCnt), // 接种1针
          stitch1PeopleRate: firstVaccines.reportRate, // 接种1针 - 占比
          stitch2People: getNumber(secondVaccines.reportCnt), // 接种2针
          stitch2PeopleRate: secondVaccines.reportRate, // 接种2针 - 占比
          stitch3People: getNumber(thirdVaccines.reportCnt), // 接种3针
          stitch3PeopleRate: thirdVaccines.reportRate, // 接种3针 - 占比
          stitch4People: getNumber(fourthVaccines.reportCnt), // 接种加强针
          stitch4PeopleRate: fourthVaccines.reportRate // 接种加强针 - 占比
        })
      }
    });
  }
  const getNumber = (num?: number) => num === undefined ? 0 : num // 0
  // 解决乘法精度丢失问题
  // 例如： 0.0167 => 1.67
  const getRateNumber = (num?: number) => {

    return parseFloat(String(num)) === 0 ? '0' : (num * 100).toFixed(1)
  }
  return (
    <Layout className='layoutPanelPm' style={{ height: 490 }}>
      <More jumpUrl={currentUser.type === 1 ? `` : `${window.location.origin}/console/personLw/base/roster`} />
      <Header className={Styles.headTitle}>
        <Radio.Group
          size='small'
          options={optionsDate}
          onChange={({ target: { value } }) => setDateType(value)}
          defaultValue={dateType}
          optionType="button"
          buttonStyle="solid"
          className={Styles.radioGroup}
        />
      </Header>

      <div>
        <div className={`${Styles.contentTitle} ${Styles.mt15}`}>
          <span>{titleData[0].name}</span>
          {/* <span>{titleData[0].percent}</span> */}
        </div>
        <Row>
          <Col span="9" >
            <PercentPie
              personCounts={getNumber(travelCard.report)}
              percent={getNumber(travelCard.reportRate)}
              bePresentCounts={getNumber(travelCard?.bePresent)}
              title='在场(人)'
              width='100%'
              height='100%'
              fontSize={14}
            />
          </Col>
          <Col span="12" style={{ height: 90 }} offset={3}>
            {/* <div className={Styles.lineBodyRight}>
              <div className={Styles.tipTop}>
                <div className={Styles.tipTopLeft}>无（人）</div>
                <div className={Styles.tipTopRight}>有（人）</div>
              </div>
              <Progress
                showInfo={false}
                percent={travelCard.leavePeopleRate}
                strokeColor='#3996ff'
                trailColor={(Number(travelCard.leavePeopleRate)===0&&Number(travelCard.existPeopleRate)===0)?'#3996ff':'#ffa200'}
                strokeLinecap='square'
              />
              <div className={Styles.tipBottom}>
                <div className={Styles.tipBottomLeft}>{getNumber(travelCard.leavePeople)}/{getNumber(travelCard.leavePeopleRate)}%</div>
                <div className={Styles.tipBottomRight}>{getNumber(travelCard.existPeople)}/{getNumber(travelCard.existPeopleRate)}%</div>
              </div>
            </div> */}
          </Col>
        </Row>
        <Row className={Styles.parentItem}>
          <span className={Styles.positionTips}>{titleData[1].name}</span>
          <Col span="9" >
            <PercentPie
              personCounts={getNumber(nucleicTest.report)}
              percent={getNumber(nucleicTest.reportRate)}
              bePresentCounts={getNumber(nucleicTest?.bePresent)}
              title='在场(人)'
              width='100%'
              height='100%'
              fontSize={14}
            />
          </Col>
          <Col span="12" style={{ height: 90 }} offset={3}>
            <div className={Styles.lineBodyRight}>
              <div className={Styles.tipTop}>
                <div className={Styles.tipTopLeft}>阴性（人）</div>
                <div className={Styles.tipTopRight}>阳性（人）</div>
              </div>
              <Progress
                showInfo={false}
                percent={nucleicTest.YinPeopleRate}
                strokeColor='#3996ff'
                trailColor={(Number(nucleicTest.YinPeopleRate) === 0 && Number(nucleicTest.YangPeopleRate) === 0) ? '#3996ff' : '#ffa200'}
                strokeLinecap='square'
              />
              <div className={Styles.tipBottom}>
                <div className={Styles.tipBottomLeft}>{getNumber(nucleicTest.YinPeople)}/{getNumber(nucleicTest.YinPeopleRate)}%</div>
                <div className={Styles.tipBottomRight}>{getNumber(nucleicTest.YangPeople)}/{getNumber(nucleicTest.YangPeopleRate)}%</div>
              </div>
            </div>
          </Col>
        </Row>
        <Row className={Styles.parentItem}>
          <span className={Styles.positionTips}>{titleData[2].name}</span>
          <Col span="9" >
            <PercentPie
              personCounts={getNumber(vaccine.report)}
              percent={getNumber(vaccine.reportRate)}
              bePresentCounts={getNumber(vaccine?.bePresent)}
              title='在场(人)'
              width='100%'
              height='100%'
              fontSize={14}
            />
          </Col>
          <Col span="12" offset={3} justify="end">
            <div className={Styles.blockList}>
              <div className={`${Styles.block}`}>
                <div className={`${Styles.blockTop}`}>{getNumber(vaccine.stitch1People)}/{getNumber(vaccine.stitch1PeopleRate)}%</div>
                <div className={`${Styles.blockBottom}`}>接种1针(人)</div>
              </div>
              <div className={`${Styles.block} ${Styles.textRight}`}>
                <div className={`${Styles.blockTop}`}>{getNumber(vaccine.stitch2People)}/{getNumber(vaccine.stitch2PeopleRate)}%</div>
                <div className={`${Styles.blockBottom}`}>接种2针(人)</div>
              </div>
              <div className={`${Styles.block}`}>
                <div className={`${Styles.blockTop}`}>{getNumber(vaccine.stitch3People)}/{getNumber(vaccine.stitch3PeopleRate)}%</div>
                <div className={`${Styles.blockBottom}`}>接种3针(人)</div>
              </div>
              <div className={`${Styles.block} ${Styles.textRight}`}>
                <div className={`${Styles.blockTop}`}>{getNumber(vaccine.stitch4People)}/{getNumber(vaccine.stitch4PeopleRate)}%</div>
                <div className={`${Styles.blockBottom}`}>接种加强针(人)</div>
              </div>
            </div>
          </Col>
        </Row>
      </div>


      <Content>

      </Content>
    </Layout>
  )
}

import React, { useState,useEffect } from "react";
import { Layout, Radio, Space, Row, Col,Divider } from 'antd';
import ReactEcharts from 'echarts-for-react';
import DescriptionsCard from '@/components/DescriptionsCard';
import opts, { axis, title, legend, color, grid } from '@/panel/echartOptions';
import { getEpidemicDataApi } from '@/services/epidemic';
import { useMount } from 'react-use';
import More from "@/components/more";
import Styles from './index.less'
import { getCurrentUser } from '@/services/api';
import defaultSetting from "@/defaultSetting";
import defaultOptions from './defaultEchartOption'
const { Header, Content } = Layout;

interface echartData{
  name:string,
  maxValue:number,
  value:number,
  percent:string}
//健康上报
export default () => {
  const currentUser = getCurrentUser();
  const [homeCount, setHomeCount] = useState<any[]>([{ label: '绿码(人)', value: 100, numStyle: { color: '#00C586' } }, { label: '黄码(人)', value: 100, numStyle: { color: '#FFA200' } }, { label: '红码(人)', value: 100, numStyle: { color: '#FF3939' } }]);
  const [temperatureCount, settemperature] = useState<any[]>([{ label: '正常(人)', value: 100, numStyle: { color: '#00C586' } },  { label: '异常(人)', value: 100, numStyle: { color: '#FF3939' } }]);
  const optionsDate = [
    { label: '全部', value: 0 },
    { label: '正式人员', value: 1 },
    { label: '临时人员', value: 2 }
  ];
  const [dateType, setDateType] = useState(0);
  const [titleData, settitleData] = useState<any>([{name:'国康码',percent:''},{name:'体温',percent:''}]);
  const [echartsOption, setEchartsOption] = useState<any>([defaultOptions,defaultOptions]);
  useEffect(() => {
    getEpidemicData()
  }, [dateType])
  const getEpidemicData=()=>{
    getEpidemicDataApi(dateType).then((res: any) => {
      const {guoKangCode,temperature} = res.result
      const guokangData:echartData = {
        name:'绿码占比',
        maxValue:guoKangCode.greenCodeNum+guoKangCode.redCodeNum+guoKangCode.yellowCodeNum,
        value:guoKangCode.greenCodeNum,
        percent:guoKangCode.greenCodeProportion
      }  
      const temperatureData:echartData = {
        name:'正常占比',
        maxValue:temperature.abnormalNum+temperature.normalNum,
        value:temperature.normalNum,
        percent:temperature.normalProportion,
      }
      setHomeCount([{ label: '绿码(人)', value: guoKangCode.greenCodeNum, numStyle: { color: '#00C586' } }, { label: '黄码(人)', value: guoKangCode.yellowCodeNum, numStyle: { color: '#FFA200' } }, { label: '红码(人)', value: guoKangCode.redCodeNum, numStyle: { color: '#FF3939' } }])
      settemperature([{ label: '正常(人)', value: temperature.normalNum, numStyle: { color: '#00C586' } },  { label: '异常(人)', value: temperature.abnormalNum, numStyle: { color: '#FF3939' } }])
      settitleData([{name:'国康码',percent:guoKangCode.reportProportion},{name:'体温',percent:temperature.reportProportion}])
      setEchartsOption([getEchartsOption(guokangData),getEchartsOption(temperatureData)])
    });
  }
    // 解决乘法精度丢失问题
  // 例如： 0.0167 => 1.67
  const getRateNumber = (num?: number) => {
   
    return parseFloat(String(num)) === 0 ? '0' : (num * 100).toFixed(1)
  }
  const getEchartsOption =(data)=>{
    console.log(data)
      return {
        // title: {
        //   text: `${data.percent*100}%\n${data.name}`,
        //   textStyle: {
        //     color: '#000',
        //     fontSize: 16,
        //     textAlign: 'center',
        //   },
         
        //  left:'center',
        //   top: 'center'
        // },
        title:{
          text: `${getRateNumber(data.percent)}%`,
          subtext: data.name || '标题',
          y: '26%',
          x: '48%',
          textAlign: 'center',
          textStyle:{
            color: '#444444',
            fontSize: 16,
            // fontSize: fontSize * 1.5,
            fontWeight: 'normal',
            // lineHeight: 15
          },
          subtextStyle:{
            color:'#666666',
            fontSize: 12,
          }
        },
        angleAxis: {
          max: data.maxValue, // 满分
          clockwise: true, // 逆时针
          // 隐藏刻度线
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            show: false
          },
          splitLine: {
            show: false
          }
        },
        radiusAxis: {
          type: 'category',
          // 隐藏刻度线
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            show: false
          },
          splitLine: {
            show: false
          }
        },
        polar: {
          center: ['50%', '50%'],
          radius: '170%' //图形大小
        },
        series: [{
          type: 'bar',
          data: [{
            name: data.name,
            value: data.value,
            itemStyle: {
              normal: {
                color: { // 完成的圆环的颜色
                  colorStops: [{
                    offset: 0,
                    color: '#14BD85' // 0% 处的颜色
                  }, {
                    offset: 1,
                    color: '#14BD85' // 100% 处的颜色
                  }]
                }
              }
            },
          }],
          coordinateSystem: 'polar',
          roundCap: true,
          barWidth: 8,
          barGap: '-100%', // 两环重叠
          radius: ['40%', '70%'],
          z: 2,
        }, { // 灰色环
          type: 'bar',
          data: [{
            value: 500,
            itemStyle: {
              color: '#E6EBF5',
            }
          }],
          coordinateSystem: 'polar',
          roundCap: true,
          barWidth: 8,
          barGap: '-100%', // 两环重叠
          radius: ['48%', '53%'],
          z: 1
        }]
      }
  }
  return (
    <Layout className='layoutPanelPm' style={{height:490}}>
      <More jumpUrl={currentUser.type === 1 ? `` : `${window.location.origin}/console/epidemic/epidemic/home`} />
      <Header className={Styles.headTitle}>
      <Radio.Group
        size='small'
        options={optionsDate}
        onChange={({ target: { value } }) => setDateType(value)}
        defaultValue={dateType}
        optionType="button"
        buttonStyle="solid"
        className={Styles.radioGroup}
      />
      </Header>
 
          <div>
            <div className={`${Styles.contentTitle} ${Styles.mt15}`}>
              <span>{titleData[0].name}</span>
              <span>{`上报占比${getRateNumber(titleData[0].percent)}%`}</span>
            </div>
          <Row>
          <Col span="13" pull={1}>
            <DescriptionsCard data={homeCount} />
          </Col>
          <Col span="11" style={{height:94}}>
          <ReactEcharts
      
          style={{ height: '100%',marginRight:'-70%' }}
          opts={opts}
          option={echartsOption[0]}
          theme="clear" />
          </Col>
        </Row>
          </div>
          <Divider/>
          <div>
            <div className={Styles.contentTitle}>
              <span>{titleData[1].name}</span>
              <span>{`上报占比${getRateNumber(titleData[1].percent)}%`}</span>
            </div>
          <Row>
          <Col span="13" pull={1}>
            <DescriptionsCard data={temperatureCount} />
          </Col>
          <Col span="11" style={{height:94}}>
          <ReactEcharts
      
          style={{ height: '100%',marginRight:'-70%'}}
          opts={opts}
          option={echartsOption[1]}
          theme="clear" />
          </Col>
        </Row>
          </div>
        
      <Content>
     
      </Content>
    </Layout>
  )
}

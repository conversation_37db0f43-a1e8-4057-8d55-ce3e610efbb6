const defaultOptions = {
  title: {
    text: '10%',
    subtext:  '标题',
    y: '26%',
    x: '48%',
    textAlign: 'center',
    textStyle:{
      color: '#444444',
      fontSize: 16,
      // fontSize: fontSize * 1.5,
      fontWeight: 'bold',
      // lineHeight: 15
    },
    subtextStyle:{
      color:'#666666',
      fontSize: 12,
    }
  },
    angleAxis: {
      max: 100, // 满分
      clockwise: true, // 逆时针
      // 隐藏刻度线
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        show: false
      },
      splitLine: {
        show: false
      }
    },
    radiusAxis: {
      type: 'category',
      // 隐藏刻度线
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        show: false
      },
      splitLine: {
        show: false
      }
    },
    polar: {
      center: ['50%', '50%'],
      radius: '170%' //图形大小
    },
    series: [{
      type: 'bar',
      data: [{
        name: '测试',
        value: 10,
        itemStyle: {
          normal: {
            color: { // 完成的圆环的颜色
              colorStops: [{
                offset: 0,
                color: '#14BD85' // 0% 处的颜色
              }, {
                offset: 1,
                color: '#14BD85' // 100% 处的颜色
              }]
            }
          }
        },
      }],
      coordinateSystem: 'polar',
      roundCap: true,
      barWidth: 8,
      barGap: '-100%', // 两环重叠
      radius: ['40%', '70%'],
      z: 2,
    }, { // 灰色环
      type: 'bar',
      data: [{
        value: 500,
        itemStyle: {
          color: '#E6EBF5',
        }
      }],
      coordinateSystem: 'polar',
      roundCap: true,
      barWidth: 8,
      barGap: '-100%', // 两环重叠
      radius: ['48%', '53%'],
      z: 1
    }]
  }
  export default defaultOptions
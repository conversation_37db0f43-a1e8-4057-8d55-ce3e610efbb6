import React, { useState, useEffect } from "react";
import { Layout, message, Select } from 'antd';
import DescriptionsCard from '@/components/DescriptionsCard';
import Gauge from './gauge';
import More from '@/components/more';
import { fetchEnvProjectMonitor, fetchEnvAllDeviceList } from '@/services/envMonitor';
import EmptyCenter from '@/components/EmptyCenter';
import { useUpdateEffect } from 'react-use';
import ProjectLess from './project.less';
import CarouselGauge, { HarmfulItem } from './carouselGauge';

interface DustItem {
  humid: number;
  tsp: number;
  tspThreshold: number;
  noise: number;
  noiseThreshold: number;
  windDirection: string;
  windSpeed: number;
  windSpeedThreshold: number;
  pmTwoPointFive: number;
  pmTwoPointFiveThreshold: number;
  pmTen: number;
  pmTenThreshold: number;
  temp: number;
  tempThreshold: number;
}

interface DeviceListItem {
  positionName: string;
  deviceSn: string;
  status: number;
  deviceType: number;
  id: number;
}

const { Header, Content } = Layout;
const STATUS_TYPES = [{ color: '#666666', text: '离线' }, { color: '#08A17B', text: '在线' }];

export default () => {

  const [data, setData] = useState<DustItem>({
    humid: 0,
    tsp: 0,
    tspThreshold: 0,
    noise: 0,
    noiseThreshold: 0,
    windDirection: '',
    windSpeed: 0,
    windSpeedThreshold: 0,
    pmTwoPointFive: 0,
    pmTwoPointFiveThreshold: 0,
    pmTen: 0,
    pmTenThreshold: 0,
    temp: 0,
    tempThreshold: 0,
  });

  const [isHaveAuthority, setIsHaveAuthority] = useState(true);
  const [deviceList, setDeviceList] = useState<DeviceListItem[]>([]);
  const [deviceId, setDeviceId] = useState<number>(0);
  const [deviceType, setDeviceType] = useState<number>(0);
  const [harmfulData, setHarmfulData] = useState<HarmfulItem[]>([]);
  // const [height, setHeight] = useState<number>(170);

  useEffect(() => {
    getEnvDeviceType();
  }, []);

  useUpdateEffect(() => {
    if (deviceId && deviceType) {
      getEnvData();
      // const parentDom = document.querySelector('.gaugeWrapper') as HTMLDivElement;
      // if (parentDom) {
      //   setHeight(parentDom.offsetHeight);
      // }
    }
  }, [deviceId, deviceType])

  const getEnvDeviceType = async () => {
    const res = await fetchEnvAllDeviceList();
    if (res?.success && res?.data) {
      setDeviceList(res.data);
      setDeviceId(res.data[0] ? res.data[0].id : undefined);
      setDeviceType(res.data[0] ? res.data[0].deviceType : undefined)
    }
  }

  const getEnvData = async () => {
    const res = await fetchEnvProjectMonitor({
      deviceId,
      deviceType
    }).catch((err: any) => {
      if (err.errorCode === '-500010') {
        setIsHaveAuthority(false);
      } else {
        message.error(err.errorMsg || err.errorMessage || '未知错误');
      }
    });
    if (res && res.success) {
      if (res.data) {
        if (deviceType === 1) {
          // 扬尘
          setData(res.data);
        } else if (deviceType === 2) {
          // 有毒有害气体
          setHarmfulData(res.data);
        }
      }
      setIsHaveAuthority(true);
    }
  }

  const handleSelectChange = (value: number, option: any) => {
    setDeviceId(value);
    setDeviceType(option.label.deviceType);
  }


  return (<>
    {isHaveAuthority ?
      <Layout className={`layoutPanelPm layoutPanelHeight ${ProjectLess.layoutPanelHeight}`}>
        <div style={{ position: 'absolute', right: 80, top: 13 }}>
          <Select value={deviceId} style={{ width: 200 }} size="small" onChange={handleSelectChange}>
            {deviceList.length ? deviceList.map(item => <Select.Option title={`${item.positionName} - ${item.deviceSn} (${STATUS_TYPES[item.status].text})`} key={item.id} value={item.id} label={item}>{item.positionName} - {item.deviceSn} ({STATUS_TYPES[item.status].text})</Select.Option>) : ''}
          </Select>
        </div>
        <More jumpUrl='/console/gddnDust/#/envMonitor/project' />
        {
          deviceList.length ?
            <>
              {data && deviceType === 1 ? <>
                <Header>
                  <div className="gaugeWrapper" style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', height: '100%', marginTop: 20 }}>
                    <Gauge key={1} style={{ height: '100%', width: '33%' }} data={[{ value: data.pmTwoPointFive || 0, name: 'PM2.5' }]} threshold={{ alarm: data.pmTwoPointFiveThreshold, max: data.pmTwoPointFiveThreshold + 50 }} />
                    <Gauge key={2} style={{ height: '100%', width: '33%' }} data={[{ value: data.pmTen || 0, name: 'PM10' }]} threshold={{ alarm: data.pmTenThreshold, max: data.pmTenThreshold + 50 }} />
                    <Gauge key={3} style={{ height: '100%', width: '33%' }} data={[{ value: data.tsp || 0, name: 'TSP' }]} threshold={{ alarm: data.tspThreshold, max: data.tspThreshold + 50 }} />
                  </div>
                </Header>
                <Content style={{ flex: 'none' }}>
                  <DescriptionsCard
                    style={{ padding: 0 }}
                    data={[
                      { label: '现场温度/湿度', value: `${data.temp || 0}℃/${data.humid || 0}%`, img: { imgUrl: require('./temperature.png'), width: 32, height: 32 }, numStyle: { color: '#333333' }, tipStyle: { color: '#666666' } },
                      { label: '现场噪音', value: `${data.noise || 0}db`, img: { imgUrl: require('./noise.png'), width: 32, height: 32 }, numStyle: { color: '#333333' }, tipStyle: { color: '#666666' } },
                      { label: '风向/风速', value: <div style={{ display: 'flex', justifyContent: 'flex-start', alignItems: 'center' }}><span style={{ fontSize: 14, display: 'inline-block', width: '60%' }}>{data.windDirection || ''}</span><span>/{data.windSpeed || 0}m/s</span></div>, img: { imgUrl: require('./wind_direction.png'), width: 32, height: 32 }, numStyle: { color: '#333333', fontSize: 20 }, tipStyle: { color: '#666666' } },
                    ]} />
                </Content></> : ''
              }
              {harmfulData.length && deviceType === 2 ?
                <div className="carouselGaugeWrapper" style={{height: '100%'}}>
                  <CarouselGauge data={harmfulData} parentDomClassName={'.carouselGaugeWrapper'} />
                </div>
                : ''
              }
            </>
          : <EmptyCenter description="当前无环境监测数据，请确认是否添加了环境监测设备" />
        }
      </Layout> : <EmptyCenter description="对不起，您当前账号无权查看该功能数据，请联系系统管理员" />}
  </>)
}

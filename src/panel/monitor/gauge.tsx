import opts from '@/panel/echartOptions';
import React, { CSSProperties } from 'react';
import ReactEcharts from 'echarts-for-react';

interface GProps {
    style: CSSProperties;
    data: { value: number; name: string; }[];
    threshold: {
        alarm: number;
        max: number;
    };
    unit?: string;
}

export default (props: GProps) => {

    const { style, data, threshold, unit } = props;

    const echartsOption = {
        series: [
            {
                name: '业务指标',
                type: 'gauge',
                radius: '100%',
                min: 0,
                max: threshold.max,
                splitNumber: 4,
                data: data.map(item => ({ ...item, name: `{name|${item.name}}\n{unit|(${unit ? unit : 'μg/m³'})}`})),
                axisLine: {
                    lineStyle: {
                        width: 8,
                        color: threshold.alarm ? [[threshold.alarm / threshold.max, '#4AAE76'], [1, '#E25F33']] : [[1, '#4AAE76']]
                    }
                },
                axisTick: {
                    lineStyle: {
                        color: 'auto'
                    }
                },
                splitLine: {
                    length: 15,
                    lineStyle: {
                        color: 'auto',
                        width: 4
                    }
                },
                axisLabel: {
                    fontSize: 12,
                    distance: 2
                },
                pointer: {
                    width: 3,
                },
                title: {
                    fontSize: 16,
                    offsetCenter: [0, -14],
                    rich: {
                        name: {
                            fontSize: 16,
                            color: '#151515'
                        },
                        unit: {
                            fontSize: 12,
                            color: '#8C8C8C'
                        }
                    }
                },
                detail: {
                    fontSize: 22,
                    offsetCenter: [0, 24]
                }
            }
        ]
    };

    return (
        <ReactEcharts
            style={{ ...style }}
            opts={opts}
            option={echartsOption}
            theme="clear" />
    );
}

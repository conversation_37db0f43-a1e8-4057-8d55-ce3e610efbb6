import React, { useState, useEffect } from 'react';
import { Carousel } from 'antd';
import Gauge from './gauge';
import CarouselGaugeLess from './carouselGauge.less';

interface CGProps {
    data: HarmfulItem[];
    parentDomClassName: string;
}

export interface HarmfulItem {
    threshold: string;
    type: string;
    value: string;
  }
  

interface GasType {
    [index: string]: { name: string; unit: string };
}

const GAS_TYPE: GasType = {
    '7': {
        name: '一氧化碳',
        unit: 'ppm'
    },
    '8': {
        name: '硫化氢',
        unit: 'ppm'
    },
    '9': {
        name: '氨气',
        unit: 'ppm'
    },
    '10': {
        name: '氧气',
        unit: '%vol'
    },
    '11': {
        name: '氢气',
        unit: '%vol'
    },
    '12': {
        name: '甲烷',
        unit: '%vol'
    }
}

export default (props: CGProps) => {

    const { data, parentDomClassName } = props;
    const [gaugeData, setGaugeData] = useState<Array<HarmfulItem[]>>([]);
    const [height, setHeight] = useState<number | string>('100%');

    useEffect(() => {
        if (data.length) {
            handleHarmfulGasData(data);
        }

        const parentDom = document.querySelector(parentDomClassName) as HTMLDivElement;
        if (parentDom) {
            setHeight(parentDom.offsetHeight);
        }
    }, [data]);

    const handleHarmfulGasData = async (data: HarmfulItem[]) => {

        const result: HarmfulItem[][] = [];
        let temp: HarmfulItem[] = [];
        data.forEach((item: HarmfulItem, index: number) => {
            if (index > 0 && index % 3 === 0) {
                result.push(temp);
                temp = [];
            }

            temp.push(item);
        })

        if (temp.length) {
            result.push(temp);
        }

        setGaugeData(result);
    }

    return (
        <div className={`carouselWrapper ${CarouselGaugeLess.carouselWrapper}`}>
            <Carousel dots={{ className: 'carouselDots' }}>
                {gaugeData.length ? gaugeData.map((item, index) => 
                    <div key={index}>
                        <div style={{ width: '100%', display: 'flex', justifyContent: item.length > 1 ? 'space-between' : 'center', alignItems: 'center', height }}>
                            {item.length ? item.map((item2, index2) => (
                            <Gauge 
                                key={index2}
                                style={{width: item.length === 3 ? '33%' : '45%', height: +height * 0.7 }}
                                data={[{ value: +item2.value || 0, name: GAS_TYPE[item2.type].name }]} threshold={{ alarm: +item2.threshold ?? 0, max: (+item2.threshold ?? 0) + 50 }}
                                unit={GAS_TYPE[item2.type].unit}
                            />)) : ''
                            }
                        </div>
                    </div>) : ''
                }
            </Carousel>
        </div>
    );
}
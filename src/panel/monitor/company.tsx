import React, { useState, useEffect } from "react";
import { Layout, Table, message, Tooltip } from 'antd';
import DescriptionsCard from '@/components/DescriptionsCard';
import More, {push} from '@/components/more';
import { fetchEnvCompanyMonitor } from '@/services/envMonitor';
import moment from 'moment';
import EmptyCenter from '@/components/EmptyCenter';

interface AlarmListItem {
  projectName: string; province: string; city: string; district: string; startTime: string; alarmType: number; projectId: number;
}

interface EnvMonitor {
  dustAlarmNum: number;
  projectNum: number;
  onlineRate: number;
  alarmList: AlarmListItem[];
}

const { Header, Content } = Layout;
// 1:tsp 2:pm2.5 3:pm10 4:温度 5:噪音 6:风速 7:一氧化碳 8:硫化氢 9:氨气 10:氧气 11:氢气 12:甲烷
const ALARM_TYPES = ['', 'TSP', 'PM2.5', 'PM10', '温度', '噪音', '风速', '一氧化碳', '硫化氢', '氨气', '氧气', '氢气', '甲烷'];

export default () => {

  const columns = [
    {
      title: '项目名称',
      dataIndex: 'projectName',
      key: 'projectName',
      ellipsis: true,
      width: 50,
      render: (text: string, row: AlarmListItem) => <Tooltip title={text}>
        <span style={{cursor: 'pointer'}} onClick={() => push(`/console/gddnDust/#/envMonitor/project/${row.projectId}/${text}`)}>{text}</span>
      </Tooltip>
    },
    {
      title: '地区',
      dataIndex: 'province',
      key: 'province',
      ellipsis: true,
      width: 50,
      render: (text: string, row: AlarmListItem) => {
        const region = text == row.city ? text : `${text},${row.city}`;
        return <Tooltip title={`${region},${row.district}`}><span>{region},{row.district}</span></Tooltip>
      }
    },
    {
      title: '超标项',
      dataIndex: 'alarmType',
      key: 'alarmType',
      ellipsis: true,
      width: 30,
      render: (text: number, row: AlarmListItem) => <Tooltip title={ALARM_TYPES[text]}><span>{ALARM_TYPES[text]}</span></Tooltip>
    },
    {
      title: '超标时间',
      dataIndex: 'startTime',
      key: 'startTime',
      ellipsis: true,
      width: 50,
      render: (text: string) => <Tooltip title={moment(text).format('YYYY-MM-DD HH:mm:ss')}><span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span></Tooltip>
    },
  ];

  const [data, setData] = useState<EnvMonitor>({
    dustAlarmNum: 0,
    projectNum: 0,
    onlineRate: 0,
    alarmList: []
  });
  const [isHaveAuthority, setIsHaveAuthority] = useState(false);

  useEffect(() => {
    getEnvData();
  }, []);

  const getEnvData = async () => {
    const res = await fetchEnvCompanyMonitor().catch((err: any) => {
      if (err.errorCode === '-500010') {
        setIsHaveAuthority(false);
      } else {
        message.error(err.errorMsg || err.errorMessage || '未知错误');
      }
    });
    if (res && res.success) {
      res.data ? setData(res.data) : '';
      setIsHaveAuthority(true);
    }
  }

  return (<>
    { isHaveAuthority ?
    <Layout className='layoutPanelPm'>
      <More content='查看更多' jumpUrl='/console/gddnDust/#/envMonitor/company' />
      {data && data.alarmList ? <><Header>
        <DescriptionsCard
          data={[
            { label: '监测项目(个)', value: data.projectNum, numStyle: { color: '#00C586' }},
            { label: '设备在线率', value: `${data.onlineRate}%`, numStyle: { color: '#FFA200' }},
            { label: '今日报警项目(个)', value: data.dustAlarmNum, numStyle: { color: '#FC6D41' }},
          ]} />
      </Header>
      <Content>
        <Table
          key="key"
          size='small'
          dataSource={data.alarmList ? data.alarmList.filter((itme, index) => index < 5) : []}
          columns={columns}
          pagination={false}
          bordered />
      </Content></> : <EmptyCenter description="当前无环境监测数据，请确认是否添加了环境监测设备" />}
    </Layout> : <EmptyCenter description="对不起，您当前账号无权查看该功能数据，请联系系统管理员" />}
  </>)
}

import More from '@/components/more'
import { getProblemInfo } from '@/services/myProblem'
import { FieldTimeOutlined, ReadOutlined } from '@ant-design/icons'
import { Empty, Layout } from 'antd'
import React, { useEffect, useState } from 'react'
import Styles from './index.less'

const { Content } = Layout
export default () => {
  const [problemList, setProblemList] = useState<any>([])  //用于展示用的列表
  const [delayCount, setDelayCount] = useState<any>()
  const [allDataList, setAllDataList] = useState<any>()  // 用于计算全部总数的列表
  const [linkUrl, setLinkUrl] = useState<any>('') // 点击更多跳转的路由
  const { getCurrentUser } = window.PMHOME;
  const currentUser = getCurrentUser();
  const getList = () => {
    getProblemInfo().then((res: any) => {
      console.log(res);
      if (res?.success) {
        let dataList = res.data.data ? res.data.data : res.data
        // console.log(dataList,'dataList');

        setAllDataList(dataList)
        setProblemList(dataList ? dataList.slice(0, 5) : "")
        let count = 0;
        dataList?.forEach((item: any) => {
          if (item.delayFlag == true) {
            count++;
          }
        })
        setDelayCount(count)
      }
    })
  }
  useEffect(() => {
    let routetUrl = '';
    routetUrl = '/console/mechatronics/integrationManagement'
    setLinkUrl(`${routetUrl}?active=2&problemStatus=0,1&includeOfme=2,3`)
  }, [])
  useEffect(() => {
    getList()
    console.log(currentUser);

  }, [])

  return (
    <div className={Styles.myQuestion}>
      <Content className={Styles.mainCon}>
        <div className={Styles.more} style={{ width: '85px', height: '30px', lineHeight: '30px', position: 'absolute', right: '0', top: '0', cursor: 'pointer' }} >
          <More content='查看更多' jumpUrl={linkUrl} />
        </div>

        {/* 统计数量部分 */}
        <div className={Styles.icons} style={{ height: '30px', display: 'flex' }}>
          <div className={Styles.firsticon}><ReadOutlined style={{ fontSize: '20px' }} />&nbsp;<span>{allDataList?.length}</span></div>
          &nbsp;&nbsp;
          <div className={Styles.seconedIcon}><FieldTimeOutlined style={{ fontSize: '20px', color: 'red' }} />&nbsp;<span>{delayCount ? delayCount : 0}</span></div>
        </div>
        {/* 列表部分 */}
        <ul className={Styles.ulCont}>
          {
            problemList.length > 0 ? problemList.map((item: any) => {
              return (
                <li className={item.delayFlag == false ? Styles.liList : Styles.liLists} key={item.problemId}>
                  <div className={Styles.lists}>
                    <div className={Styles.listsName}>{item.problemName}</div>
                    <div className={Styles.listsTime}>{item.finalDate}</div>
                  </div>
                </li>
              )
            }) : <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
          }
        </ul>
      </Content>
    </div>
  )
}

.notice {
  height: 20px;
  width: 20px;
  background: #FDB553;
  text-align: center;
  line-height: 20px;
  color: #fff;
  border-radius: 50%;
  font-size: 12px;
  position: relative;
}
.unread {
  position: absolute;
  top: 18px;
  left: 90px;
  background: red;
  z-index: 1;
  line-height: 20px;
  border-radius: 50%;
  display: block;
  width: 20px;
  height: 20px;
  color: #fff;
  text-align: center;
  font-size: 12px;
}
.noticeTable {
  :global(.ant-table-tbody) {
    tr>td {
      border: 0;
    }
  }
}

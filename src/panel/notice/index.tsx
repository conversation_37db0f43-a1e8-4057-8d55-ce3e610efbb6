import React, { useState, useEffect } from "react";
import {Badge, Layout, Table, Typography} from 'antd';
import StylesNotice from './index.less';
import {SoundFilled} from '@ant-design/icons';
import {noticeList} from '@/services/news';
import moment from 'moment'
import More from "@/components/more";
import defaultSetting from '@/defaultSetting';

const {Content } = Layout;
const {Text} = Typography

interface NoticeObj {
  name: string
  data: string
  status: number
  id: number
}


export default () => {

  const [dataList, setDataList] = useState<NoticeObj[]>([])
  const [count, setCount] = useState(0)

  useEffect(() => {
    noticeList({
      currentPage: 1,
      pageSize: 8,
      isMessageCenter: 1
    }).then((res: any) => {
      if (res.list && res.list.length > 0) {
        setDataList(res.list)
        setCount(res.query.unread_count | 0)
      }
    })
  }, [])

  const columns: any = [
    {
      dataIndex: 'read',
      key: 'read',
      width: '10%',
      render: (read: number) => {
        return  <div className={StylesNotice.notice}>
          <Badge dot={read === 1}><SoundFilled /></Badge>
        </div>;
      }
    },
    {
      dataIndex: 'title',
      ellipsis: true,
      key: 'title',
      width: '50%',
    },
    {
      dataIndex: 'gmtCreate',
      ellipsis: true,
      key: 'gmtCreate',
      width: '40%',
      render: (gmtCreate: number) => {
        return <Text disabled>{moment(gmtCreate).format('YYYY-MM-DD HH:mm')}</Text>
      }
    },
  ];
  return (
    <Layout>
      <div className={StylesNotice.unread} style={{display: count ? 'block' : 'none'}}>{count}</div>
      <More content='查看更多' jumpUrl="/console/oa/#/notice" />
      <Content>
        <Table
          className={StylesNotice.noticeTable}
          size='small'
          showHeader={false}
          dataSource={dataList}
          columns={columns}
          pagination={false}
          onRow={record => {
            return {
              onClick: () => {location.href = `${defaultSetting.routes.console}oa/#/notice/noticeDetails?notice_id=${record.id}`}
            }
          }}
        />
      </Content>
    </Layout>
  )
}

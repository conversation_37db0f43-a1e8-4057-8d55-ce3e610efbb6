import React, { useEffect, useState } from "react";
import { Layout, message, Tooltip } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import DescriptionsCard from '@/components/DescriptionsCard';
import LineChart from '@/components/charts/line';
import { fetchVideoProjectMonitor } from '@/services/videoMonitor';
import { VideoMonitor } from './type';
import More from '@/components/more';
import EmptyCenter from '@/components/EmptyCenter';
import defaultSetting from "@/defaultSetting";

const { Header, Content } = Layout;

export default () => {

  const [data, setData] = useState<VideoMonitor>({
    onlineProjectNum: 0,
    currentOnlineRate: 0,
    currentOnlineVideoNum: 0,
    weekOnlineTrend: []
  });

  const [isHaveAuthority, setIsHaveAuthority] = useState(false);

  useEffect(() => {
    getVideoData();
  }, []);

  const getVideoData = async () => {
    const res = await fetchVideoProjectMonitor().catch((err: any) => {
      if (err.errorCode === '-500010') {
        setIsHaveAuthority(false);
      } else {
        message.error(err.errorMsg || err.errorMessage || '未知错误');
      }
    });
    if (res && res.success) {
      res.data ? setData(res.data) : '';
      setIsHaveAuthority(true);
    }
  }

  return (<>
    { isHaveAuthority ?
    <Layout className='layoutPanelPm'>
      <More jumpUrl={`${defaultSetting.routes.console}videoCenter/#/videoCenter`} />
      {data && data.weekOnlineTrend && data.weekOnlineTrend.length ? <><Header>
        <DescriptionsCard
          data={[
            { 
              label: '监控在线率', value: data.platformType === 3 ? '--' : `${data.currentOnlineRate}%`, numStyle: { color: '#FFA200' }, tipStyle: { color: '#666666' },
              tooltipNode: data.platformType === 3 ?
                <Tooltip title="视频监控在线率说明：因萤石云不支持获取设备在线状态，目前无法监测监控在线率，7日趋势按100%计算。">
                  <QuestionCircleOutlined style={{color: '#1890ff', cursor: 'pointer'}} />
                </Tooltip> : ''
            },
            { label: '在线摄像头(路)', value: data.currentOnlineVideoNum, numStyle: { color: '#00C586' }, tipStyle: { color: '#666666' } },
          ]}
        />
      </Header>
      <Content>
        <LineChart data={data.weekOnlineTrend ? data.weekOnlineTrend.map(item => item.onlineRate || 0) : []} category={data.weekOnlineTrend ? data.weekOnlineTrend.map(item => item.reportDate) : []}/>
      </Content></> : <EmptyCenter description="近七日无在线视频监控" /> }
    </Layout> : <EmptyCenter description="对不起，您当前账号无权查看该功能数据，请联系系统管理员" /> }
  </>)
}

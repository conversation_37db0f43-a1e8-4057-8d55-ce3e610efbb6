import { Empty, Layout, Table } from 'antd';
import React, { useState, useMemo } from 'react';
import { axis, grid } from '@/panel/echartOptions';
import { useRequest } from 'ahooks';
import More from '@/components/more/index';
import ReactEcharts from 'echarts-for-react';
import { getDataZoom } from './slider';
import { getBtdjyhzgqk } from '@/services/risk';
import { getCurrentUser } from '@/services/api';

const { Content } = Layout;
const hazardCorrectionByLevel = () => {
  const [dataSource, setDataSource] = useState([]);
  const currentUser = getCurrentUser();

  const { data } = useRequest(async () => {
    const { projectId, coId } = currentUser;
    const res: any = await getBtdjyhzgqk({ companyId: coId, projectId });
    setDataSource(res.data);
  });

  const config = useMemo(() => {
    return {
      grid,
      tooltip: {
        trigger: 'axis',
        formatter: function (params) {
          var relVal = params[0].name;
          for (var i = 0; i < params.length; i++) {
            relVal +=
              '<br/>' + params[i].marker + params[i].seriesName + ' : ' + params[i].value + '%';
          }
          return relVal;
        },
      },
      legend: {
        data: ['整改完成率', '整改及时率'],
        itemWidth: 10,
        itemHeight: 10,
      },
      xAxis: {
        type: 'category',
        show: true,
        interval: 0,
        ...axis,
        data: dataSource?.map((x) => x.level_name),
      },
      yAxis: {
        type: 'value',
        name: '%',
        ...axis,
        nameTextStyle: {
          color: '#666666',
        },
      },
      series: [
        {
          name: '整改完成率',
          data: dataSource?.map((x) => x.complt_rate),
          type: 'bar',
          barMinWidth: 14,
          barMaxWidth: 14,
          color: '#49C292',
        },
        {
          name: '整改及时率',
          data: dataSource?.map((x) => x.ontime_complt_rate),
          type: 'bar',
          barMinWidth: 14,
          barMaxWidth: 14,
          color: '#FFCD00',
        },
      ],
      dataZoom: getDataZoom(
        {
          sliderSize: 8,
          isSliderFromEnd: false,
        },
        dataSource.length,
      ),
    };
  }, [dataSource]);

  function jumpPage(url: string) {
    localStorage.setItem('checkItem', '1');
    window.location.replace(url);
  }

  return (
    <Layout className="layoutPanelPm">
      <Content style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
        {/* <More jumpUrl="/console/inspect/#/safety" onClick={jumpPage} /> */}
        {dataSource?.length ? (
          <ReactEcharts style={{ width: '100%', height: 300 }} option={config} />
        ) : (
          <Empty />
        )}
      </Content>
    </Layout>
  );
};

export default hazardCorrectionByLevel;

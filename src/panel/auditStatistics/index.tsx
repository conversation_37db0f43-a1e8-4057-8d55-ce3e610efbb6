import * as echarts from 'echarts';

import React, { useEffect, useState } from 'react';

import ReacrEcharts from 'echarts-for-react';
import Styles from './index.less';
import { getApprove } from '@/services/myApprove';

type EchartOption = echarts.EChartsOption;

export default function index() {
  const [chartData, setChartData] = useState<any>({});

  const getApproveToDo = () => {
    // 获取待执行流程数据
    getApprove().then((res: any) => {
      if (res.success) {
        setChartData(res.data);
      }
    });
  };

  const proStatistic = () => {
    const option: EchartOption = {
      legend: {
        orient: 'vertical',
        left: 'right',
      },
      tooltip: {
        trigger: 'item',
      },
      series: [
        {
          type: 'pie',
          radius: '70%',
          label: {
            show: true,
            position: 'inner',
            formatter: '{d}%',
          },
          data: [
            { value: chartData?.done ?? 0, name: '已处理', itemStyle: { color: '#49c292' } },
            { value: chartData?.todo ?? 0, name: '未处理', itemStyle: { color: '#ff9400' } },
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
            },
          },
        },
      ],
    };
    return option;
  };
  useEffect(() => {
    getApproveToDo();
  }, []);
  return (
    <div className={Styles.auditStatistical}>
      <ReacrEcharts option={proStatistic()} />
    </div>
  );
}

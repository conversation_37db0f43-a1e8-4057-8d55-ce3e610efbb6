import React from "react";
import ReactEcharts from 'echarts-for-react';
import { Layout } from 'antd';
import opts, {color} from '@/panel/echartOptions';

const {Content } = Layout;
export default () => {
  const echartsOption = {
    tooltip: {
      axisPointer: {
        type: 'cross',
      }
    },
    color,
    series: [
      {
        type: 'pie',
        radius: ['60%', '80%'],
        center: ['50%', '50%'],
        label: {
          color: '#666',
          fontSize: 14,
          formatter: '{b}：{c}'
        },
        data: [
          {name: '停工', value: 2},
          {name: '无计划', value: 4},
          {name: '进度滞后', value: 5},
          {name: '进度正常', value: 12},
          {name: '进度提前', value: 5},
        ]
      },
    ]
  }
  return (
    <Layout className='layoutPanelPm'>
      <Content>
        <ReactEcharts
          style={{height: '100%'}}
          opts={opts}
          option={echartsOption}
          theme="clear"/>
      </Content>
    </Layout>
  )
}

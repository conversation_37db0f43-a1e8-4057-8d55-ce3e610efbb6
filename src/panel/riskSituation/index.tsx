import { Empty, Layout, Radio } from 'antd';
import React, { useState, useMemo } from 'react';
import { axis, grid } from '@/panel/echartOptions';
import { useRequest } from 'ahooks';
import More from '@/components/more/index';
import ReactEcharts from 'echarts-for-react';
import { getDataZoom } from './slider';
import { getFxgkqkZzt } from '@/services/risk';
import { getCurrentUser } from '@/services/api';
import styles from './index.less';

const { Content } = Layout;

const RadioGroup = ({ value, onChange }) => {
  return (
    <div className={styles.radio}>
      <Radio.Group
        size="small"
        optionType="button"
        options={[
          { label: '实施中', value: 1 },
          { label: '未开始', value: 2 },
          { label: '已结束', value: 3 },
        ]}
        onChange={onChange}
        value={value}
      />
    </div>
  );
};

const RiskSituation = () => {
  const [value, setValue] = useState(1);
  const [dataSource, setDataSource] = useState([]);
  const currentUser = getCurrentUser();

  const { data } = useRequest(
    async () => {
      const res: any = await getFxgkqkZzt({ type: value, companyId: currentUser.coId });
      setDataSource(res.data);
    },
    {
      refreshDeps: [value],
    },
  );

  const config = useMemo(() => {
    return {
      grid,
      tooltip: {
        trigger: 'axis',
        formatter: function (params) {
          var relVal = params[0].name;
          for (var i = 0; i < params.length; i++) {
            relVal +=
              '<br/>' + params[i].marker + params[i].seriesName + ' : ' + params[i].value + '个';
          }
          return relVal;
        },
      },
      legend: {
        data: ['风险总数', '重大风险数'],
        itemWidth: 10,
        itemHeight: 10,
      },
      xAxis: {
        type: 'category',
        show: true,
        interval: 0,
        ...axis,
        data: dataSource?.map((x) => x.department_name),
      },
      yAxis: {
        type: 'value',
        name: '数量/个',
        ...axis,
        nameTextStyle: {
          color: '#666666',
        },
      },
      series: [
        {
          name: '风险总数',
          data: dataSource?.map((x) => x.risk_num),
          type: 'bar',
          barMinWidth: 14,
          barMaxWidth: 14,
          color: '#945FB9',
        },
        {
          name: '重大风险数',
          data: dataSource?.map((x) => x.serious_risk_num),
          type: 'bar',
          barMinWidth: 14,
          barMaxWidth: 14,
          color: '#FF4D4F',
        },
      ],
      dataZoom: getDataZoom(
        {
          sliderSize: 8,
          isSliderFromEnd: false,
        },
        dataSource.length,
      ),
    };
  }, [dataSource]);

  function jumpPage(url: string) {
    localStorage.setItem('checkItem', '1');
    window.location.replace(url);
  }
  const handleChange = (e) => {
    const { value } = e.target;
    setValue(value);
  };

  return (
    <Layout className="layoutPanelPm">
      <Content style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
        {/* <More jumpUrl="/console/inspect/#/safety" onClick={jumpPage} /> */}
        <RadioGroup value={value} onChange={handleChange} />
        {dataSource?.length ? (
          <ReactEcharts style={{ width: '100%', height: 300 }} option={config} />
        ) : (
          <Empty />
        )}
      </Content>
    </Layout>
  );
};

export default RiskSituation;

import React, { useState, useEffect, useMemo } from "react";
import { Layout, Select, message } from 'antd';
import DescriptionsCard from '@/components/DescriptionsCard';
import LineChart from '@/components/charts/line';
import More from '@/components/more';
import { fetchElevatorCompanyMonitor, fetchTowerCompanyMonitor, fetchTowerProjectMonitor, fetchElevatorProjectMonitor } from '@/services/devicesMonitor';
import { ElevatorMonitor } from './type';
import { getCurrentUser } from '@/services/api';
import EmptyCenter from '@/components/EmptyCenter';

const { Header, Content } = Layout;
const { Option } = Select;

const DEVICES_SERVER: any = [{}, {
    'tower': fetchTowerCompanyMonitor,
    'elevator': fetchElevatorCompanyMonitor
}, {
    'tower': fetchTowerProjectMonitor,
    'elevator': fetchElevatorProjectMonitor
}]

const getUrl = (selectType: string, type: number) => {
    if (selectType === 'tower') {
        return type === 1 ? '/console/gddnTower/#/gddnTower/towercompany' : '/console/gddnTower/#/gddnTower/towercrane';
    } else {
        return type === 1 ? '/console/gddnElevator/#/gddnElevator/elevatorcompany' : '/console/gddnElevator/#/gddnElevator/elevator';
    }
}

export default () => {

    const [data, setData] = useState<ElevatorMonitor>({
        deviceNum: 0,
        onlineRate: 0,
        alarmNum: 0,
        dataList: []
    });
    const currentUser = getCurrentUser();
    const [selectType, setSelectType] = useState('tower');
    const [linkUrl, setLinkUrl] = useState(getUrl(selectType, currentUser.type));
    const [isHaveAuthority, setIsHaveAuthority] = useState(false);

    useEffect(() => {
        // 切换url
        setLinkUrl(getUrl(selectType, currentUser.type));
        // 获取数据
        getDevicesData();
    }, [selectType]);

    const getDevicesData = async () => {
        const res = await DEVICES_SERVER[currentUser && currentUser.type ? currentUser.type : 1][selectType]().catch((err: any) => {
            if (err.errorCode === '-500010') {
                setIsHaveAuthority(false);
            } else {
                message.error(err.errorMsg || err.errorMessage || '未知错误');
            }
        });

        if (res && res.success) {
            res.data ? setData(mapFields(res.data)) : '';
            setIsHaveAuthority(true);
        }
    }

    // 映射字段以及跳转的url
    const mapFields = (data: ElevatorMonitor) => {
        const result: ElevatorMonitor = {
            deviceNum: data.deviceNum,
            onlineRate: data.onlineRate,
            alarmNum: data.alarmNum,
        };

        if (selectType === 'tower') {
            result.dataList = data.curves;
        } else {
            result.dataList = data.dataList;
        }
        return result;
    }

    const handleSelectChange = (type: string) => {
        setSelectType(type);
    }

    return useMemo(() => {
        return (
            <Layout className='layoutPanelPm'>
                <div style={{ position: 'absolute', left: 100, top: 22 }}>
                    <Select defaultValue={selectType} style={{ width: 120 }} size="small" onChange={handleSelectChange}>
                        <Option value="tower">塔式起重机</Option>
                        <Option value="elevator">施工升降机</Option>
                    </Select>
                </div>
                { isHaveAuthority ? <>
                <More jumpUrl={linkUrl} />
                {data && data.dataList ? <><Header>
                    <DescriptionsCard
                        data={[
                            { label: '在场设备(台)', value: data.deviceNum, numStyle: { color: '#00C586' }, tipStyle: { color: '#666666' } },
                            { label: '监控在线率', value: `${data.onlineRate}%`, numStyle: { color: '#FFA200' }, tipStyle: { color: '#666666' } },
                            { label: '今日监控报警(次)', value: data.alarmNum, numStyle: { color: '#FC6D41' }, tipStyle: { color: '#666666' } },
                        ]} />
                </Header>
                <Content>
                    <LineChart data={data.dataList ? data.dataList.map(item => item.onlineRate) : []} category={data.dataList ? data.dataList.map(item => item.reportDate) : []} />
                </Content></> : <EmptyCenter description="近七日无监测数据" /> }
                </> : <EmptyCenter description="对不起，您当前账号无权查看该功能数据，请联系系统管理员" /> }
            </Layout>
        )
    }, [data, selectType, isHaveAuthority, data.dataList]);
}

const { request } = window.PMHOME;
const plugServer = '/plug/api';
/**
 * 应用中心 - 记录插件使用
 */
export async function recordPlugUse() {
  return request(`${plugServer}/manage/plug/getPlugUseTop.htm`, {
    method: 'POST',
  });
}

/**
 * 请求图片svg数据
 * @param url 请求地址
 * @returns 图片svg数据
 */
export const requestXMLHttpRequest = (url: string): Promise<string> => {
  return new Promise((resolve) => {
    const ajax = new XMLHttpRequest()
    ajax.open('get', url)
    ajax.responseType = 'text'
    ajax.send()
    ajax.onreadystatechange = () => {
      if (ajax.readyState === 4 && ajax.status === 200) {
        resolve(ajax.response)
      }
    }
  })
}

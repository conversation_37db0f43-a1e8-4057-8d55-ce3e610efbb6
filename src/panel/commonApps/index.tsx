/*
 * @LastEditors: <Your Name>
 * @Description: 最近访问 页面
 */
import { windowOpen } from '@/utils/utils';
import { Tooltip } from 'antd';
import React, { useEffect, useState } from 'react';
import { recordPlugUse, requestXMLHttpRequest } from './api';
import Styles from './index.less';

const VisitCenter: React.FC = () => {
  const [plugData, setPlugData] = useState([]);
  // 用于存储每个 item 的图片 URL
  const [imgUrls, setImgUrls] = useState<{ [key: string]: string }>({});

  const fetchData = async () => {
    const res = await recordPlugUse();
    setPlugData(res?.data || []);
  };

  useEffect(() => {
    // 获取每个 item 的图片 svg 地址
    const fetchImgUrls = async () => {
      const newImgUrls: { [key: string]: string } = {};
      for (const item of plugData) {
        if (item.logoUuidFileUrl) {
          const url = await requestXMLHttpRequest(item.logoUuidFileUrl);
          const svgWithSize = url.replace('<svg', '<svg width="48" height="48"');
          newImgUrls[item.plugId] = svgWithSize;
        }
      }
      setImgUrls(newImgUrls);
    };
    // 当 plugData 改变时，重新获取图片 URL
    if (plugData.length > 0) {
      fetchImgUrls();
    }
  }, [plugData]);

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <div className={Styles.commonAppsContent}>
      {plugData?.map((item: any) => {
        const imgUrl = imgUrls[item.plugId] || item.logoUuidFileUrl;
        return (
          <div
            className={Styles.commonAppsItem}
            key={item.plugId}
            onClick={() => {
              windowOpen(item.webUrl);
            }}
          >
            <div className={Styles.commonAppsImg}>
              <div
                dangerouslySetInnerHTML={{
                  __html: imgUrl,
                }}
              ></div>
            </div>
            <Tooltip title={item.plugName}>
              <div className={Styles.commonAppsTitle}>{item.plugName}</div>
            </Tooltip>
          </div>
        );
      })}
    </div>
  );
};

export default VisitCenter;

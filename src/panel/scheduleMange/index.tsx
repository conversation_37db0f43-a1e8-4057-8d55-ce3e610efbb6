import React, { useState } from 'react';
import { Row, Col } from 'antd';
import { getProjectDistribution, getTaskWarn } from '@/services/progress';
import { useEffectOnce } from 'react-use';
import ReactEcharts from 'echarts-for-react';
import opts from '@/panel/echartOptions';
import { Layout } from 'antd';
import More from '@/components/more';
interface Ires {
  code?: number;
  data: any;
  message?: string;
  success?: boolean;
  list?: any;
  errorMsg?: string
}
interface IData {
  name: string;
  value: number;
}

export default () => {
  const [parData, setParData] = useState<IData[]>([]);
  const [legendData, setLegendData] = useState<string[]>([]);
  const [parDataTwo, setParDataTwo] = useState<IData[]>([]);
  const [legendDataTwo, setLegendDataTwo] = useState<string[]>([]);
  const [optionTwoTitleText, setOptionTwoTitleText] = useState(0);
  const { Content } = Layout;

  let formatNumber = function(num: number) {
    let reg = /(?=(\B)(\d{3})+$)/g;
    return num.toString().replace(reg, ',');
  }
  let total = parData.reduce((a, b) => {
    return a + b.value * 1
  }, 0);

  const tooltip = { trigger: 'item', formatter: '{b}' }
  const color = ['#00C083', '#FFBD3F'];
  const textStyle = {
    rich: {
      name: {
        fontSize: 12,
        fontWeight: 'normal',
        color: '#666666',
      },
      val: {
        fontSize: 30,
        fontWeight: 'bold',
        color: '#333333',
        padding: [10, 0]
      }
    }
  }

  const echartsOption = {
    tooltip: tooltip,
    color: color,
    title: [{
      text: '{val|' + formatNumber(total) + '}\n{name|' + '项目总数' + '}',
      top: '21%',
      left: 'center',
      textStyle: textStyle
    }],
    series: [{
      type: 'pie',
      radius: ['38%', '53%'],
      center: ['50%', '32%'],
      data: parData,
      hoverAnimation: false,
      labelLine: {
        show: false
      },
      label: {
        show: false
      },
    }],
    legend: {
      orient: 'vertical',
      left: 'center',
      top: '60%',
      data: legendData,
      itemWidth: 16,
      itemHeight: 16,
      borderRadius: 0
    },
  };

  const optionTwo = {
    color: [...color, '#FF993F', '#FC6D41'],
    tooltip: tooltip,
    title: [{
      text: '{val|' + optionTwoTitleText + '}\n{name|' + '节点受控率(%)' + '}',
      top: '21%',
      left: 'center',
      textStyle: textStyle
    }],
    series: [{
      type: 'pie',
      radius: ['38%', '53%'],
      center: ['50%', '32%'],
      data: parDataTwo,
      hoverAnimation: false,
      labelLine: {
        show: false
      },
      label: {
        show: false
      },
    }],
    legend: {
      orient: 'horizontal',
      left: 'center',
      top: '60%',
      data: legendDataTwo,
      itemWidth: 16,
      itemHeight: 16,
      borderRadius: 0,
      width: 320,
    },
  };

  useEffectOnce(() => {
    getParData()
  })

  function getParData() {
    getProjectDistribution().then((res: Ires) => {
      if (res.success) {
        const { data } = res;
        let parOneStrOne = `进度正常 ${data.normalScale ? data.normalScale : 0}%, ${data.normal}个`,
          parOneStrTwo = `进度延误 ${data.delayScale ? data.delayScale : 0}%, ${data.delay}个`;
        setParData([{ name: parOneStrOne, value: data.normal }, { name: parOneStrTwo, value: data.delay }]);
        setLegendData([parOneStrOne, parOneStrTwo]);
      }
    })
    getTaskWarn().then((res: Ires) => {
      if (res.success) {
        const { data } = res;
        let parTwoStrOne = `正常完成 ${data.normalTaskRate}%, ${data.normalTaskCount}个`,
          parTwoStrTwo = `一般延误 ${data.generalTaskRate}%, ${data.generalTaskCount}个`,
          parTwoStrThree = `严重延误 ${data.largerTaskRate}%, ${data.largerTaskCount}个`,
          parTwoStrFour = `重大延误 ${data.greatTaskRate}%, ${data.greatTaskCount}个`;
        setParDataTwo([{ name: parTwoStrOne, value: data.normalTaskCount },
          { name: parTwoStrTwo, value: data.generalTaskCount },
          { name: parTwoStrThree, value: data.largerTaskCount },
          { name: parTwoStrFour, value: data.greatTaskCount }]);
        setLegendDataTwo([parTwoStrOne, parTwoStrTwo, parTwoStrThree, parTwoStrFour]);
        setOptionTwoTitleText(data.nodeControlledRate);
      }
    })
  }


  return (
    <Layout className='layoutPanelPm'>
      <Content>
        <More content='查看更多' jumpUrl="/console/schedule-plan/#/schedule-plan/" />
        <Row>
          <Col span={12}>
            <Content>
              <ReactEcharts
                opts={opts}
                option={echartsOption}
                theme="clear"/>
            </Content>
          </Col>
          <Col span={12}>
            <Content>
              <ReactEcharts
                opts={opts}
                option={optionTwo}
                theme="clear"/>
            </Content>
          </Col>
        </Row>
      </Content>
    </Layout>
  )
}

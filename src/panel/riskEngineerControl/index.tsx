import { Empty, Layout, Radio, Spin } from 'antd';
import React, { useState, useMemo } from 'react';
import { axis, grid } from '@/panel/echartOptions';
import { useRequest } from 'ahooks';
import More from '@/components/more/index';
import ReactEcharts from 'echarts-for-react';
import { getDataZoom } from './slider';
import { getWdgcFagkqs } from '@/services/risk';
import { getCurrentUser } from '@/services/api';
import styles from './index.less';

const { Content } = Layout;

const RadioGroup = ({ value, onChange }) => {
  return (
    <div className={styles.radio}>
      <Radio.Group
        size="small"
        optionType="button"
        options={[
          { label: '危大方案', value: 1 },
          { label: '旁站记录', value: 2 },
          { label: '危大验收', value: 3 },
        ]}
        onChange={onChange}
        value={value}
      />
    </div>
  );
};

const lengedData = [
  ['方案管控缺失数', '缺失率'],
  ['旁站管控缺失数', '缺失率'],
  ['验收不通过数', '不通过率'],
];

const lengedColor = [
  {
    color1: '#2478FF',
    color2: '#FFCD00',
  },
  {
    color1: '#2fc0f9',
    color2: '#FFCD00',
  },
  {
    color1: '#ff9400',
    color2: '#ff4d4f',
  },
];

const RiskEngineerControl = () => {
  const [dataSource, setDataSource] = useState([]);
  const [value, setValue] = useState(1);
  const currentUser = getCurrentUser();
  const [loading, setLoading] = useState(false);

  const { data } = useRequest(
    async () => {
      const res: any = await getWdgcFagkqs({ projectId: currentUser.projectId });
      setDataSource(res.data);
      setLoading(false);
    },
    {
      refreshDeps: [value],
    },
  );

  const config = useMemo(() => {
    return {
      grid,
      tooltip: {
        trigger: 'axis',
        formatter: function (params) {
          var relVal = params[0].name;
          for (var i = 0; i < params.length; i++) {
            relVal +=
              '<br/>' +
              params[i].marker +
              params[i].seriesName +
              ' : ' +
              params[i].value +
              (i === 0 ? '个' : '%');
          }
          return relVal;
        },
      },
      legend: {
        data: lengedData[value - 1],
        itemWidth: 10,
        itemHeight: 10,
      },
      xAxis: {
        type: 'category',
        show: true,
        interval: 0,
        ...axis,
        data: dataSource?.map((x) => x.date),
      },
      yAxis: [
        {
          type: 'value',
          name: '数量/个',
          ...axis,
          nameTextStyle: {
            color: '#666666',
          },
        },
        {
          type: 'value',
          name: '缺失率/%',
          ...axis,
          nameTextStyle: {
            color: '#666666',
          },
        },
      ],
      series: [
        {
          name: lengedData[value - 1][0],
          data: dataSource?.map((x) => {
            if (value === 1) return x.schemeMissCount;
            if (value === 2) return x.standMissCount;
            return x.noPassAcceptanceCount;
          }),
          type: 'bar',
          barMinWidth: 14,
          barMaxWidth: 14,
          color: lengedColor[value - 1].color1,
        },
        {
          name: lengedData[value - 1][1],
          data: dataSource?.map((x) => {
            if (value === 1) return x.controlMissRate;
            if (value === 2) return x.standMissRate;
            return x.noCompletedRate;
          }),
          type: 'line',
          barMinWidth: 14,
          barMaxWidth: 14,
          yAxisIndex: 1,
          color: lengedColor[value - 1].color2,
        },
      ],
      dataZoom: getDataZoom(
        {
          sliderSize: 8,
          isSliderFromEnd: false,
        },
        dataSource.length,
      ),
    };
  }, [dataSource]);

  function jumpPage(url: string) {
    localStorage.setItem('checkItem', '1');
    window.location.replace(url);
  }

  const handleChange = (e) => {
    const { value } = e.target;
    setValue(value);
    setLoading(true);
  };

  return (
    <Layout className="layoutPanelPm">
      <Content style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
        {/* <More jumpUrl="/console/inspect/#/safety" onClick={jumpPage} /> */}
        <RadioGroup value={value} onChange={handleChange} />
        {loading ? (
          <div className={styles.spin_style}>
            <Spin spinning={loading} />
          </div>
        ) : dataSource?.length ? (
          <ReactEcharts style={{ width: '100%', height: 300 }} option={config} />
        ) : (
          <Empty />
        )}
      </Content>
    </Layout>
  );
};

export default RiskEngineerControl;

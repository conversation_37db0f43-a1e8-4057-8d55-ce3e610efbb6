.content {
  :global {
    .ant-carousel .slick-dots li button {
      background: #909399 !important;
    }
  }
  .wrap {
    height: 330px;
    .statistics {
      display: flex;
      .item {
        flex: 1;
        font-size: 14px;
        text-align: center;
        .count {
          margin-right: 8px;
          font-weight: 500;
          font-size: 24px;
        }
      }
    }
    .title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin: 10px 2px;
      .text {
        position: relative;
        padding-left: 12px;
        &:before {
          position: absolute;
          top: 0;
          left: 0;
          width: 4px;
          height: 22px;
          background-color: #2478ff;
          content: '';
        }
      }
    }
  }
}

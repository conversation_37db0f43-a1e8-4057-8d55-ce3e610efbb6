import { Bar, DualAxes } from '@ant-design/plots';
import { Carousel } from 'antd';
import moment from 'moment';
import React, { useEffect, useState } from 'react';
import styles from './index.less';

// assets
import More from '@/components/more';
import TitleTooltip from '@/components/titleTooltip';
import {
  getCoverRateApi,
  getHiddedAndRectifictionApi,
  getHiddenRectificationApi,
  getStatisticsApi,
} from './assets/api';
import { headerConfigs, headerConfigs1 } from './assets/configs';
import { createDualAxesConfigs, createGroupBarConfigs } from './assets/utils';

export default () => {
  const [baseParams] = useState({
    startDate: moment(new Date()).format('YYYY-MM') + '-01',
    endDate: moment(new Date()).format('YYYY-MM-DD'),
  });
  const [info, setInfo] = useState<any>({});
  const [info1, setInfo1] = useState<any>({});
  const [dataList1, setDataList1] = useState<any[]>([]);
  const [dataList2, setDataList2] = useState<any[]>([]);

  // 获取统计
  const getStatistics = () => {
    getStatisticsApi(baseParams)
      .then((res: any) => {
        const { success, result = {} } = res || {};
        if (success) {
          setInfo(result);
        }
      })
      .catch((err: any) => {
        setInfo({});
      });
  };
  // 隐患统计
  const getHiddedAndRectifiction = () => {
    getHiddedAndRectifictionApi(baseParams).then((res: any) => {
      const { success, result = {} } = res || {};
      if (success) {
        setInfo1(result);
      }
    });
  };
  // 统计
  const getCoverRate = () => {
    getCoverRateApi(baseParams)
      .then((res: any) => {
        const { success, result = [] } = res || {};
        if (success) {
          const _dataList = result.map((el: any) => ({
            id: el.departmentId,
            label: el.departmentName,
            column: el.checkEdProjectCount,
            line: Number(el.checkProjectCoverRate),
          }));
          setDataList1(_dataList);
        }
      })
      .catch((err: any) => {
        setDataList1([]);
      });
  };
  // 隐患统计
  const getHiddenRectification = () => {
    getHiddenRectificationApi({
      ...baseParams,
      orgType: 1,
    }).then((res: any) => {
      const { success, result = [] } = res || {};
      if (success) {
        setDataList2(result);
      }
    });
  };
  useEffect(() => {
    getStatistics();
    getHiddedAndRectifiction();
    getCoverRate();
    getHiddenRectification();
  }, []);

  const renderContent1 = () => (
    <section className={styles.wrap}>
      <section className={styles.statistics}>
        {headerConfigs.map((el) => (
          <section key={el.key} className={styles.item}>
            <h6>
              <span>{el.label}</span>
            </h6>
            <aside>
              <span className={styles.count}>{info?.[el.key] || 0}</span>
              <span>{el.unit}</span>
            </aside>
          </section>
        ))}
      </section>
      <section className={styles.title}>
        <span className={styles.text}>企业检查覆盖率排行</span>
      </section>
      <section>
        <DualAxes style={{ height: 190 }} {...createDualAxesConfigs(dataList1)} />
      </section>
    </section>
  );

  const renderContent2 = () => (
    <section className={styles.wrap}>
      <section className={styles.statistics}>
        {headerConfigs1.map((el) => (
          <section key={el.key} className={styles.item}>
            <h6>
              <span>{el.label}</span>
            </h6>
            <aside>
              <span className={styles.count}>{info1?.[el.key] || 0}</span>
              <span>{el.unit}</span>
            </aside>
          </section>
        ))}
      </section>
      <section className={styles.title}>
        <span className={styles.text}>企业隐患整改排行</span>
      </section>
      <section>
        <Bar style={{ height: 190 }} {...createGroupBarConfigs(dataList2)} />
      </section>
    </section>
  );

  return (
    <div>
      <More content="详情" jumpUrl="/console/agilebpm-web/#/?appVersionId=1669259807604916225" />
      <TitleTooltip text="默认统计本月数据" />
      <section className={styles.content}>
        <Carousel autoplay={true}>
          <div>{renderContent1()}</div>
          <div>{renderContent2()}</div>
        </Carousel>
      </section>
    </div>
  );
};

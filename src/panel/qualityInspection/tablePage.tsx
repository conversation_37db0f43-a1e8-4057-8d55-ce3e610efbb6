import { Carousel, Radio, Table } from 'antd';
import moment from 'moment';
import React, { useEffect, useState } from 'react';
import styles from './index.less';

// assets
import TitleTooltip from '@/components/titleTooltip';
import {
  getHiddedAndRectifictionApi,
  getHiddenRectificationApi,
  getProjectCheckPassRateApi,
  getStatisticsApi,
} from './assets/api';
import { headerConfigs, headerConfigs1 } from './assets/configs';

export default () => {
  const [baseParams, setBaseParams] = useState({
    startDate: moment(new Date()).format('YYYY-MM') + '-01',
    endDate: moment(new Date()).format('YYYY-MM-DD'),
    orderAsc: 'desc',
  });
  const [info, setInfo] = useState<any>({});
  const [info1, setInfo1] = useState<any>({});
  const [dataList1, setDataList1] = useState<any[]>([]);
  const [dataList2, setDataList2] = useState<any[]>([]);

  // 获取统计
  const getStatistics = () => {
    getStatisticsApi(baseParams)
      .then((res: any) => {
        const { success, result = {} } = res || {};
        if (success) {
          setInfo(result);
        }
      })
      .catch((err: any) => {
        setInfo({});
      });
  };
  // 隐患统计
  const getHiddedAndRectifiction = () => {
    getHiddedAndRectifictionApi(baseParams).then((res: any) => {
      const { success, result = {} } = res || {};
      if (success) {
        setInfo1(result);
      }
    });
  };
  // 统计
  const getProjectCheckPassRate = () => {
    getProjectCheckPassRateApi({ ...baseParams, checkType: 1, orgType: 2 })
      .then((res: any) => {
        const { success, result = [] } = res || {};
        if (success) {
          setDataList1(result);
        }
      })
      .catch((err: any) => {
        setDataList1([]);
      });
  };
  // 隐患统计
  const getHiddenRectification = () => {
    getHiddenRectificationApi({
      ...baseParams,
      orgType: 2,
    }).then((res: any) => {
      const { success, result = [] } = res || {};
      if (success) {
        setDataList2(result);
      }
    });
  };
  useEffect(() => {
    getStatistics();
    getHiddedAndRectifiction();
  }, []);
  useEffect(() => {
    getProjectCheckPassRate();
    getHiddenRectification();
  }, [baseParams]);

  const onChangeRadio = (e: any) => {
    const value = e.target.value;
    setBaseParams((prev) => ({
      ...prev,
      orderAsc: value,
    }));
  };

  const columns1: any[] = [
    {
      title: '项目名称',
      dataIndex: 'orgName',
    },
    {
      title: '检查次数',
      dataIndex: 'checkCount',
      align: 'right',
      width: 110,
    },
    {
      title: '项目自检达标率',
      dataIndex: 'rate',
      align: 'right',
      width: 140,
      render: (_, record) => (record.rate * 100).toFixed(2) + '%',
    },
  ];

  const columns2: any[] = [
    {
      title: '项目名称',
      dataIndex: 'orgName',
    },
    {
      title: '隐患总数',
      dataIndex: 'hiddenCount',
      align: 'right',
      width: 110,
    },
    {
      title: '隐患整改率',
      dataIndex: 'rectificationRate',
      align: 'right',
      width: 110,
      render: (_, record) => record.rectificationRate + '%',
    },
    {
      title: '一次整改完成率',
      dataIndex: 'onceRectificationRate',
      align: 'right',
      width: 140,
      render: (_, record) => record.onceRectificationRate + '%',
    },
  ];

  const renderContent1 = () => (
    <section className={styles.wrap}>
      <section className={styles.statistics}>
        {headerConfigs.map((el) => (
          <section key={el.key} className={styles.item}>
            <h6>
              <span>{el.label}</span>
            </h6>
            <aside>
              <span className={styles.count}>{info?.[el.key] || 0}</span>
              <span>{el.unit}</span>
            </aside>
          </section>
        ))}
      </section>
      <section className={styles.title}>
        <span className={styles.text}>项目自检达标率排行（前十）</span>
        <Radio.Group value={baseParams.orderAsc} onChange={onChangeRadio}>
          <Radio.Button value={'asc'}>升序</Radio.Button>
          <Radio.Button value={'desc'}>降序</Radio.Button>
        </Radio.Group>
      </section>
      <section>
        <Table
          rowKey="orgName"
          dataSource={dataList1}
          columns={columns1}
          pagination={false}
          scroll={{ y: 100 }}
        />
      </section>
    </section>
  );

  const renderContent2 = () => (
    <section className={styles.wrap}>
      <section className={styles.statistics}>
        {headerConfigs1.map((el) => (
          <section key={el.key} className={styles.item}>
            <h6>
              <span>{el.label}</span>
            </h6>
            <aside>
              <span className={styles.count}>{info1?.[el.key] || 0}</span>
              <span>{el.unit}</span>
            </aside>
          </section>
        ))}
      </section>
      <section className={styles.title}>
        <span className={styles.text}>项目隐患整改率排行（前十）</span>
        <Radio.Group value={baseParams.orderAsc} onChange={onChangeRadio}>
          <Radio.Button value={'asc'}>升序</Radio.Button>
          <Radio.Button value={'desc'}>降序</Radio.Button>
        </Radio.Group>
      </section>
      <section>
        <Table
          rowKey="orgName"
          dataSource={dataList2}
          columns={columns2}
          pagination={false}
          scroll={{ y: 100 }}
        />
      </section>
    </section>
  );

  return (
    <div>
      {/* <More content="详情" jumpUrl="/console/agilebpm-web/#/?appVersionId=1657992927354265602" /> */}
      <TitleTooltip text="默认统计本月数据" />
      <section className={styles.content}>
        <Carousel autoplay={true}>
          <div>{renderContent1()}</div>
          <div>{renderContent2()}</div>
        </Carousel>
      </section>
    </div>
  );
};

const { request } = window.PMHOME;

// 企业检查统计
export const getStatisticsApi = (data: any) => {
  return request(`/metapen-analysis/api/quality/check/statistics`, {
    method: 'POST',
    data,
  });
};

// 隐患统计
export const getHiddedAndRectifictionApi = (data: any) => {
  return request(`/metapen-analysis/api/quality/check/hiddedAndRectifiction`, {
    method: 'POST',
    data,
  });
};

// 企业检查覆盖率排行榜
export const getCoverRateApi = (data: any) => {
  return request(`/metapen-analysis/api/quality/check/coverRate`, {
    method: 'POST',
    data,
  });
};

// 隐患整改情况统计
export const getHiddenRectificationApi = (data: any) => {
  return request(`/metapen-analysis/api/quality/check/hiddenRectificationOrder`, {
    method: 'POST',
    data,
  });
};

// 项目自检达标率统计
export const getProjectCheckPassRateApi = (data: any) => {
  return request(`/metapen-analysis/api/quality/check/projectCheckPassRateOrder`, {
    method: 'POST',
    data,
  });
};

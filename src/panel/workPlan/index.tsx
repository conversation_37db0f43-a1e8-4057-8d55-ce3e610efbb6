import More from '@/components/more'
import { FieldTimeOutlined, ReadOutlined } from '@ant-design/icons'
import { Layout } from 'antd'
import React, { useEffect, useState } from 'react'
import Styles from './index.less'
import { getWorkPlanInfo } from '../../services/workPlan'
import jump from '@/pages/jump'
import Empty from 'antd/es/empty'

const { Content } = Layout
export default () => {
  const [workPlanInfo, setWorkPlanInfo] = useState<any>([])  // 用于存储工作计划的数据
  const [delayCount, setDelayCount] = useState<any>() // 存储已延期的总数
  const [fileId, setFileId] = useState<any>() //进度文件ID
  const [linkUrl, setLinkUrl] = useState<any>('') // 点击更多跳转的路由



  // 获取工作计划的方法
  const getWorkInfo = () => {
    getWorkPlanInfo().then((res: any) => {
      if (res?.success) {
        let dataSource: any[] = []
        dataSource = res.data.planList
        setWorkPlanInfo(dataSource ? dataSource.slice(0, 5) : "")
        let count = 0
        dataSource?.forEach((element: any) => {
          if ((element.completion == 1 || element.completion == 2) && element.delayType == 2) {
            count++
          }
          // console.log(element, 'element');
          setFileId(element.planFileId)
        })
        setDelayCount(count)
      }
    })
  }
  useEffect(() => {
    let routetUrl = '';
    // /progressManagementPlus/progressEdit?personValue=1,2&checkQuery=2
    routetUrl = '/console/mechatronics/progressManagementPlus/progressEdit'
    setLinkUrl(`${routetUrl}?personValue=1,2&checkQuery=2`)
  }, [fileId])

  useEffect(() => {
    getWorkInfo()
  }, [])

  return (
    <div className={Styles.workPlan}>
      <Content className={Styles.mainCon}>
        <div className={Styles.more} style={{ width: '85px', height: '30px', lineHeight: '30px', position: 'absolute', right: '0', top: '0', cursor: 'pointer' }} >
          <More content='查看更多' jumpUrl={linkUrl} />
        </div>
        <div className={Styles.icons} style={{ height: '30px', display: 'flex' }}>
          <div className={Styles.firsticon}><ReadOutlined style={{ fontSize: '20px' }} />&nbsp;<span>{workPlanInfo.length}</span></div>
          &nbsp;&nbsp;
          <div className={Styles.seconedIcon}><FieldTimeOutlined style={{ fontSize: '20px', color: 'red' }} />&nbsp;<span>{delayCount ? delayCount : 0}</span></div>
        </div>

        <ul className={Styles.ulCont}>
          {
            workPlanInfo.length > 0 ? workPlanInfo.map((item: any) => {
              return <li className={(item.completion == 1 || item.completion == 2) && item.delayType == 2 ? Styles.liLists : Styles.liList} key={item.id}>
                <div className={Styles.lists}>
                  <div className={Styles.listsName}>{item.taskName}</div>
                  <div className={Styles.listsTime}>{item.finishDate}</div>
                </div>
              </li>
            }) : <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
          }
        </ul>
      </Content>
    </div>
  )
}

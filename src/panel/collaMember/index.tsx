import React, { useEffect, useMemo, useState } from 'react';

import { collaGetMemberTaskListPm } from '@/services/colla';
import { message } from 'antd';
import ReacrEcharts from 'echarts-for-react';
import styles from './index.less';

const CollaModeling = () => {
  const [chartData, setChartData] = useState<any[]>([]);

  const getEchartsData = async () => {
    try {
      const response: any = await collaGetMemberTaskListPm();
      if (response.success) {
        setChartData(response.data ?? []);
      } else {
        message.error(response.errMessage);
      }
    } catch {}
  };

  const options = useMemo(() => {
    let createProblems: number[] = [];
    let fixProblems: number[] = [];
    let offProblems: number[] = [];
    let nameList: string[] = [];
    chartData.forEach((list) => {
      // if (list.id == 1) {
      createProblems.push(list.doingCount);
      // }
      // if (list.id == 2) {
      fixProblems.push(list.doneCount);
      // }
      offProblems.push(list.delayCount);

      nameList.push(list.workSetUserName);
    });
    return {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          // Use axis to trigger tooltip
          type: 'shadow', // 'shadow' as default; can also be 'line' or 'shadow'
        },
      },
      legend: {},
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: {
        type: 'value',
        axisLine: { lineStyle: { color: 'rgb(162,162,162)' } },
        axisLabel: { color: 'rgb(162,162,162)' },
        splitLine: {
          lineStyle: { color: 'rgb(229,229,229)' },
        },
      },
      yAxis: {
        type: 'category',
        data: nameList,
        axisLine: { show: false },
        axisLabel: { color: 'rgb(97,97,97)' },
      },
      series: [
        {
          name: '进行中',
          type: 'bar',
          stack: 'total',
          label: {
            show: true,
            formatter: function (params) {
              if (params.value > 0) {
                return params.value;
              } else {
                return '';
              }
            },
          },
          emphasis: {
            focus: 'series',
          },
          itemStyle: {
            normal: { color: '#7599ea' },
          },
          data: createProblems,
        },
        {
          name: '已完成',
          type: 'bar',
          stack: 'total',
          label: {
            show: true,
            formatter: function (params) {
              if (params.value > 0) {
                return params.value;
              } else {
                return '';
              }
            },
          },
          emphasis: {
            focus: 'series',
          },
          itemStyle: {
            normal: { color: '#6fcfa1' },
          },
          data: fixProblems,
        },
        {
          name: '已延期',
          type: 'bar',
          stack: 'total',
          label: {
            show: true,
            formatter: function (params) {
              if (params.value > 0) {
                return params.value;
              } else {
                return '';
              }
            },
          },
          emphasis: {
            focus: 'series',
          },
          itemStyle: {
            normal: { color: '#546387' },
          },
          data: offProblems,
        },
      ],
    };
  }, [chartData]);

  useEffect(() => {
    getEchartsData();
  }, []);

  return (
    <div className={styles.bimProblemTrend}>
      <ReacrEcharts option={options} />
    </div>
  );
};

export default CollaModeling;

import React, { useEffect, useMemo, useState } from 'react';

import ReacrEcharts from 'echarts-for-react';
import { message } from 'antd';
import styles from './index.less';
import { userDocTypeStatsStatsService } from '@/services/bim';

const BimFileUpload = () => {
  const [chartData, setChartData] = useState<any[]>([]);

  const getEchartsData = async () => {
    try {
      const response: any = await userDocTypeStatsStatsService();
      if (response.success) {
        setChartData(response.data ?? []);
      } else {
        message.error(response.errMessage);
      }
    } catch {}
  };

  const options = useMemo(() => {
    let legendData = [];
    let bims: number[] = [];
    let dwgs: number[] = [];
    let offices: number[] = [];
    let medias: number[] = [];
    let others: number[] = [];
    if (chartData.length > 0) {
      legendData = chartData[0].children.map((item: any) => item.name);
      chartData.forEach((item) => {
        item.children.forEach((list: any) => {
          if (list.name == '模型') {
            bims.push(list.value);
          }
          if (list.name == '图纸') {
            dwgs.push(list.value);
          }
          if (list.name == '文档') {
            offices.push(list.value);
          }
          if (list.name == '多媒体') {
            medias.push(list.value);
          }
          if (list.name == '其他') {
            others.push(list.value);
          }
        });
      });
    }
    return {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          // 坐标轴指示器，坐标轴触发有效
          type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
        },
      },
      dataZoom:
        chartData.length > 6
          ? [
              {
                type: 'inside',
                startValue: 0,
                endValue: 5,
                minValueSpan: 5,
                maxValueSpan: 5,
                yAxisIndex: [0],
                zoomOnMouseWheel: false, // 关闭滚轮缩放
                moveOnMouseWheel: true, // 开启滚轮平移
                moveOnMouseMove: true, // 鼠标移动能触发数据窗口平移
              },
              {
                type: 'slider',
                realtime: true,
                startValue: 0,
                endValue: 5,
                width: '3.5',
                height: '80%',
                yAxisIndex: [0], // 控制y轴滚动
                fillerColor: 'rgba(154, 181, 215, 1)', // 滚动条颜色
                borderColor: 'rgba(17, 100, 210, 0.12)',
                backgroundColor: '#cfcfcf', //两边未选中的滑动条区域的颜色
                handleSize: 0, // 两边手柄尺寸
                showDataShadow: false, //是否显示数据阴影 默认auto
                showDetail: false, // 拖拽时是否展示滚动条两侧的文字
                top: '1%',
                right: '5',
              },
            ]
          : [],
      legend: {
        data: legendData,
      },
      grid: {
        top: 40,
        left: 16,
        right: '4%',
        bottom: 40,
        containLabel: true,
      },
      xAxis: {
        type: 'value',
        axisLine: { show: false },
        axisTick: { show: false },
        axisLabel: { color: 'rgb(162,162,162)' },
        splitLine: {
          lineStyle: { color: 'rgb(229,229,229)' },
        },
      },
      yAxis: {
        type: 'category',
        data: chartData.map((item) => item.name),
        inverse: true,
        axisTick: { show: false },
        axisLine: {
          lineStyle: { color: 'rgb(229,229,229)' },
        },
        axisLabel: { color: 'rgb(97,97,97)' },
      },
      series: [
        {
          name: '模型',
          type: 'bar',
          stack: 'file',
          label: {
            show: true,
            position: 'inside',
            formatter: (params: any) => {
              if (params.value == 0) {
                return '';
              }
              return params.value;
            },
          },
          barMaxWidth: 25,
          itemStyle: {
            normal: { color: 'rgba(91, 143, 249, 1)' },
          },
          data: bims,
        },
        {
          name: '图纸',
          type: 'bar',
          stack: 'file',
          label: {
            show: true,
            position: 'inside',
            formatter: (params: any) => {
              if (params.value == 0) {
                return '';
              }
              return params.value;
            },
          },
          barMaxWidth: 25,
          itemStyle: {
            normal: { color: 'rgba(90, 216, 166, 1)' },
          },
          data: dwgs,
        },
        {
          name: '文档',
          type: 'bar',
          stack: 'file',
          label: {
            show: true,
            position: 'inside',
            formatter: (params: any) => {
              if (params.value == 0) {
                return '';
              }
              return params.value;
            },
          },
          barMaxWidth: 25,
          itemStyle: {
            normal: { color: 'rgba(93, 112, 146, 1)' },
          },
          data: offices,
        },
        {
          name: '多媒体',
          type: 'bar',
          stack: 'file',
          label: {
            show: true,
            position: 'inside',
            formatter: (params: any) => {
              if (params.value == 0) {
                return '';
              }
              return params.value;
            },
          },
          barMaxWidth: 25,
          itemStyle: {
            normal: { color: 'rgba(246, 189, 22, 1)' },
          },
          data: medias,
        },
        {
          name: '其他',
          type: 'bar',
          stack: 'file',
          label: {
            show: true,
            position: 'inside',
            formatter: (params: any) => {
              if (params.value == 0) {
                return '';
              }
              return params.value;
            },
          },
          barMaxWidth: 25,
          itemStyle: {
            normal: { color: 'rgba(232, 100, 82, 1)' },
          },
          data: others,
        },
      ],
    };
  }, [chartData]);

  useEffect(() => {
    getEchartsData();
  }, []);

  return (
    <div className={styles.bimFileUpload}>
      <div className={styles.echartsBox}>
        <ReacrEcharts option={options} />
      </div>
      <div className={styles.desc}>上传文件数量（个）</div>
    </div>
  );
};

export default BimFileUpload;

import React, { useState, useEffect } from 'react'
import './index.less'
import * as Map from '@pms/backbone-map-dom';
import { findProjectStat, findCompanyStatProjectList } from '../../services/projectview'

// 项目概况
const Index = (props: any) => {
	const { PmsMap, PmsMarker, PmsInfoWindow, PmsCustomOverlay }: any = Map
	const [dataList, setdataList] = useState<any[]>([
		{
			title: '在建项目',
			data: '0',
			dataColor: '#0B8CFF'
		},
		{
			title: '重点项目',
			data: '0',
			dataColor: '#FF0000'
		},
		{
			title: '停工项目',
			data: '0',
			dataColor: '#989898'
		},
		{
			title: '海外项目',
			data: '0',
			dataColor: '#0B8CFF'
		},
		{
			title: '本月新建项目',
			data: '0',
			dataColor: '#2AC5F9'
		},
		{
			title: '本月竣工项目',
			data: '0',
			dataColor: '#00B589'
		},
	])
	const [projectList, setprojectList] = useState<any[]>([])
	const [mapInfo, setmapInfo] = useState({
		zoom: 0
	})
	const [selectedProject, setselectedProject] = useState<any>({})

	const gotoProject = () => {
		const user = window.__PMS_CONSOLE__ ? window.__PMS_CONSOLE__.user : {}
		const history = window.__PMS_CONSOLE__ ? window.__PMS_CONSOLE__.history : {}
		// console.log(selectedProject, user)
		user
			.postDomain(
				2,
				selectedProject.projectId,
			)
			.then(() => history.goHome())
	}

	const getProjectStatistic = () => {
		findProjectStat().then((res: any) => {
			// console.log(res)
			setdataList(res.data)
		})
	}

	const getProjectList = () => {
		findCompanyStatProjectList().then((res: any) => {
			// console.log('项目列表', res.data)
			setprojectList(res.data)
		})
		// const list = [
		// 	{
		// 		projectId: 12844,
		// 		projectName: '铁路工程',
		// 		superior: '上级组织名称',
		// 		pointx: 30.229057,
		// 		pointy: 120.125683,
		// 		canEnter: true
		// 	},
		// 	{
		// 		projectId: 12843,
		// 		projectName: '公路工程-路线',
		// 		superior: '上级组织名称1',
		// 		pointx: 30.229556,
		// 		pointy: 121.124354,
		// 		canEnter: false
		// 	},
		// 	{
		// 		projectId: 17521,
		// 		projectName: '坐标远点的项目',
		// 		superior: '上级组织名称2',
		// 		pointx: 31.092489,
		// 		pointy: 121.117188,
		// 		canEnter: false
		// 	}
		// ]
		// setprojectList(list)
	}

	useEffect(() => {
		getProjectStatistic()
		getProjectList()
	}, [])

	useEffect(() => {
		let zoom = 4
		if (props.location && props.location.width === 4) {
			zoom = 5
		}
		setmapInfo({
			zoom
		})
	}, [props])



	return (
		mapInfo.zoom && (
			<div className='project-overview'>
				<div className="project-overview-left">
					{
						dataList.map((item) => {
							return (
								<div className="project-overview-left-item">
									<div
										className="project-overview-left-item-data"
										style={{ color: item.dataColor ? item.dataColor : undefined }}
									>
										{item.data}
									</div>
									<div className="project-overview-left-item-title">
										{item.title}
									</div>
								</div>
							)
						})
					}
				</div>
				<div className="project-overview-right">
					<PmsMap
						id="map-wrap"
						center={{ lng: 108, lat: 34 }}
						width={'100%'}
						height={'100%'}
						zoom={mapInfo.zoom}
						onClick={(e: any) => {
							// console.log('map click', e);
							setselectedProject({})
						}}
					>
						{
							selectedProject.projectId && (
								<PmsCustomOverlay
									position={{ lng: selectedProject.pointy, lat: selectedProject.pointx }}
									offset={[0, 0]}
								>
									<div style={{ width: 215, boxSizing: 'border-box', padding: '10px 15px', background: '#fff', position: 'relative', top: '-25px', boxShadow: '0 0 10px' }}>
										<div>
											{selectedProject.projectName}
										</div>
										<div style={{ margin: '5px 0' }}>
											上级组织：{selectedProject.superior || '-'}
										</div>
										{
											selectedProject.canEnter && (
												<div>
													<a onClick={gotoProject}>
														进入项目 {'>'}
													</a>
												</div>
											)
										}
									</div>
								</PmsCustomOverlay>
								// <PmsInfoWindow
								// 	content={
								// 		`<div>
								// 			<div>
								// 				${selectedProject.projectName}
								// 			</div>
								// 			<div>
								// 				上级组织：${selectedProject.superior}
								// 			</div>
								// 			<div>
								// 				<a>进入项目</a>
								// 			</div>
								// 		</div>
								// 		`
								// 	}
								// 	visible={true}
								// 	position={{ lng: selectedProject.pointy, lat: selectedProject.pointx }}
								// 	onClose={() => {
								// 		console.log('infoWindow close');
								// 		setselectedProject({})
								// 	}}
								// />
							)
						}
						{
							projectList.map((item) => {
								return (
									<PmsMarker
										position={{ lng: item.pointy, lat: item.pointx }}
										onClick={(e: any) => {
											e.domEvent?.stopPropagation();
											// console.log('marker click', e, item);
											setselectedProject({})
											setselectedProject(item)
										}}
									/>
								)
							})
						}

						{/* <PmsMarker
						// iconOpts={{
						// 	// url: Red,
						// 	url: 'https://lf3-cdn-tos.bytescm.com/obj/static/xitu_juejin_web/24127194d5b158d7eaf8f09a256c5d01.svg',
						// 	size: [1, 1]
						// }}
						position={{ lng: 116.399, lat: 39.91 }}
						// label={{
						// 	content: '这是一个标记点',
						// 	offset: [0, -50],
						// }}
						onClick={(e: any) => {
							e.domEvent?.stopPropagation();
							console.log('marker click', e);
						}}
					/> */}
					</PmsMap>
				</div>
			</div>
		)
	)
}

export default Index

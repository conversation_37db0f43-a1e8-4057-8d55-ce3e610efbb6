import React from "react";
import {Layout, Table, Progress, Tag} from 'antd';
import {round} from 'lodash';

const {Content } = Layout;
export default () => {
  const dataSource = [
    {
      key: '1',
      name: '国际森林康养旅游度假区项目',
      date: '2020-2-2',
      address: '四川省,雅安市',
      schedule: 0.88,
      status: 1,
    },
    {
      key: '2',
      name: '国际森林康养旅游度假区项目',
      date: '2020-2-2',
      address: '四川省,雅安市',
      schedule: 0.56,
      status: 2,
      day: 21,
    },
    {
      key: '3',
      name: '国际森林康养旅游度假区项目',
      date: '2020-2-2',
      address: '四川省,雅安市',
      schedule: 0.44,
      status: 2,
      day: 24,
    },
    {
      key: '4',
      name: '国际森林康养旅游度假区项目',
      date: '2020-2-2',
      address: '四川省,雅安市',
      schedule: 0.34,
      status: 3,
      day: 6,
    },
    {
      key: '5',
      name: '国际森林康养旅游度假区项目',
      date: '2020-2-2',
      address: '四川省,雅安市',
      schedule: 0.32,
      status: 3,
      day: 4,
    },
  ];

  const columns: any = [
    {
      title: '项目名称',
      dataIndex: 'name',
      ellipsis: true,
      key: 'name',
    },
    {
      title: '区域',
      dataIndex: 'address',
      key: 'address',
      width: '20%',
    },
    {
      title: '计划竣工日期',
      dataIndex: 'date',
      key: 'date',
      width: '20%',
    },
    {
      title: '工期进度',
      dataIndex: 'schedule',
      key: 'schedule',
      width: '20%',
      render: (data: number) => {
        return <Progress percent={round(data * 100)} />
      }
    },
    {
      title: '项目状态',
      key: 'status',
      width: '15%',
      align: 'center',
      render: (data: any) => {
        switch (data.status) {
          case 1:
            return <Tag style={{width: 78, textAlign: 'center', marginRight: 0}} color="#00C083">正常</Tag>;
          case 2:
            return <Tag style={{width: 78, textAlign: 'center', marginRight: 0}} color="#FC6D41" >滞后{data.day}天</Tag>;
          case 3:
            return <Tag style={{width: 78, textAlign: 'center', marginRight: 0}} color="#FFBD3F" >提前{data.day}天</Tag>;
        }
      }
    },
  ];
  return (
    <Layout className='layoutPanelPm'>
      <Content>
        <Table
          size='small'
          dataSource={dataSource}
          columns={columns}
          pagination={false}
        />
      </Content>
    </Layout>
  )
}

import EmptyCenter from '@/components/EmptyCenter';
import More from '@/components/more';
import { getScheduleRankNodeControl } from '@/services/progress';
import { Layout, Tabs } from 'antd';
import React, { useState } from 'react';
import { useEffectOnce } from 'react-use';
import ourStyle from '../scheduleRanking/index.less';
import PaintingBar from '../scheduleRanking/PaintingBar';
const { Content } = Layout;
interface Ires {
  code?: number;
  data: any;
  message?: string;
  success?: boolean;
  list?: any;
  errorMsg?: string;
}
export default () => {
  const { TabPane } = Tabs;
  const [nodeArr, setrNodeArr] = useState([]);
  const [nodeTwoArr, setrNodeTwoArr] = useState([]);

  useEffectOnce(() => {
    getLavelData(1);
    getLavelTwoData(2);
  });

  function getLavelData(nodeLevel: number) {
    getScheduleRankNodeControl({ page: 1, pageSize: 5, nodeLevel }).then((res: Ires) => {
      if (res.success && res?.data) {
        const {
          data: { dataList },
        } = res;
        setrNodeArr(dataList);
      }
    });
  }

  function getLavelTwoData(nodeLevel: number) {
    getScheduleRankNodeControl({ page: 1, pageSize: 5, nodeLevel }).then((res: Ires) => {
      if (res.success && res?.data) {
        const {
          data: { dataList },
        } = res;
        setrNodeTwoArr(dataList);
      }
    });
  }

  function goDetail(projectId: number) {
    const url = `${window.location.protocol}//${window.location.host}/console/schedule-plan/#/schedule-plan/proDetails?pjId=${projectId}`;
    window.location.replace(url);
  }

  function loopDom(x: any) {
    return (
      <div className={`${ourStyle.warp_div_son} ${ourStyle.mt10}`} key={`levelOne-${x.projectId}`}>
        <div
          style={{ width: '150px' }}
          className={ourStyle.ellipsis}
          onClick={() => goDetail(x.projectId)}
        >
          <span title={x.projectName}>{x.projectName}</span>
        </div>
        <div style={{ flex: 1, marginTop: '6px' }}>
          <PaintingBar firstWidth={x.scale1} firstColor="#69A8FF" />
        </div>
        <div style={{ width: '70px' }}>
          <span style={{ paddingLeft: '8px', width: '70px', display: 'inline-block' }}>
            {x.scale1.toFixed(1)} %
          </span>
        </div>
      </div>
    );
  }

  return (
    <Layout className="layoutPanelPm">
      <Content style={{ display: 'flex' }}>
        <More content="查看更多" jumpUrl="/console/schedule-plan/#/schedule-plan/" />
        <div style={{ width: '100%' }}>
          <Tabs>
            <TabPane tab="一级节点受控率" key="4">
              <div className={ourStyle.c_over_y}>
                {nodeArr.map((x: any) => {
                  return loopDom(x);
                })}
              </div>
              {nodeArr.length === 0 && <EmptyCenter description="暂无数据" />}
            </TabPane>
            <TabPane tab="二级节点受控率" key="5">
              <div className={ourStyle.c_over_y}>
                {nodeTwoArr.map((x: any) => {
                  return loopDom(x);
                })}
              </div>
              {nodeTwoArr.length === 0 && <EmptyCenter description="暂无数据" />}
            </TabPane>
          </Tabs>
        </div>
      </Content>
    </Layout>
  );
};

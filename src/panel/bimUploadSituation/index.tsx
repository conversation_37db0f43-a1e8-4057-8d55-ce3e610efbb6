import { companyDocProjectUploadStatsService, companyDocUploadStatsService } from '@/services/bim';
import { message, Tooltip } from 'antd';
import React, { useEffect, useMemo, useState } from 'react';

import ReacrEcharts from 'echarts-for-react';
import styles from './index.less';

let observer: any = null;

const BimProblemTrack = () => {
  const [chartData, setChartData] = useState<any[]>([]);
  const [listData, setListData] = useState<any[]>([]);
  const [isHideList, setHideList] = useState<boolean>(false);

  const getEchartsData = async () => {
    try {
      const response: any = await companyDocUploadStatsService();
      if (response.success) {
        setChartData(response.data ?? []);
      } else {
        message.error(response.errMessage);
      }
    } catch {}
  };

  const getListData = async () => {
    try {
      const response: any = await companyDocProjectUploadStatsService();
      if (response.success) {
        setListData(response.data ?? []);
      } else {
        message.error(response.errMessage);
      }
    } catch {}
  };

  const options = useMemo(() => {
    return {
      color: 'rgb(85,137,243)',
      grid: {
        top: 40,
        left: 16,
        right: '4%',
        bottom: 40,
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: chartData.map((item) => item.name),
        axisTick: { show: false },
        axisLabel: { color: 'rgb(155,155,155)' },
        splitLine: {
          lineStyle: { color: 'rgb(229,229,229)' },
        },
        axisLine: {
          lineStyle: { color: 'rgb(155,155,155)' },
        },
      },
      yAxis: {
        type: 'value',
        axisLine: { show: false },
        axisTick: { show: false },
        axisLabel: { color: 'rgb(162,162,162)' },
      },
      series: [
        {
          data: chartData.map((item) => item.value),
          label: {
            show: true,
            position: 'top',
          },
          type: 'bar',
        },
      ],
    };
  }, [chartData]);

  useEffect(() => {
    getEchartsData();
    getListData();

    let element = document.getElementById('bimUploadSituation') as any;
    observer = new MutationObserver(domResize);
    observer.observe(element, {
      attributes: true,
      childList: true,
      subtree: true,
    });

    return () => {
      // 之后，可停止观察
      observer.disconnect();
    };
  }, []);

  const domResize = () => {
    let dom = document.getElementById('bimUploadSituation');
    if (dom) {
      if (dom.clientWidth < 500) {
        setHideList(true);
      } else {
        setHideList(false);
      }
    }
  };

  useEffect(() => {
    if (listData.length > 0 && !isHideList) {
      createStyle();
    }
  }, [isHideList, listData]);

  /**
   * 根据索引值动态生成动画标签
   */
  const createStyle = () => {
    let lists = listData
      .map((item: any, index: number) => {
        return { ...item, index };
      })
      .filter((item: any) => item.name.length > 8)
      .map((item: any) => item.index);
    let frames = ``;
    lists.forEach((item) => {
      let width = document.getElementById(`bimUploadSituation${item}`)?.clientWidth ?? 0;
      frames =
        frames +
        `
      @keyframes uploadscrollT${item} {
        0% {
          left: ${width + 32}px;
        }
        100% {
          left: 0;
        }
      }

      @keyframes uploadscroll${item} {
        0% {
          left: 0;
        }
        100% {
          left: -${width + 32}px;
        }
      }
    `;
    });
    // 创建style标签
    const style = document.createElement('style');
    // 将 keyframes样式写入style内
    style.innerHTML = frames;
    // 将style样式存放到head标签
    document.getElementsByTagName('head')[0].appendChild(style);
  };

  return (
    <div className={styles.bimUploadSituation} id="bimUploadSituation">
      <div className={styles.container}>
        <div
          className={styles.chartBox}
          style={{ width: isHideList ? '100%' : 'calc(100% - 240px)' }}
        >
          <ReacrEcharts option={options} />
        </div>
        <div className={styles.listBox} style={{ display: isHideList ? 'none' : 'block' }}>
          <div className={styles.title}>项目新增文件排名</div>
          <div className={styles.list}>
            {listData.map((item, index) => {
              return (
                <div className={styles.box} key={item.strId}>
                  <div className={styles.leftBox}>
                    <div
                      className={
                        item.order <= 3 ? `${styles.serialNo} ${styles.bg}` : styles.serialNo}
                    >
                      {item.order}
                    </div>
                    {item.name.length > 8 ? (
                      <Tooltip title={item.name}>
                        <div className={`${styles.name} ${styles.tip}`}>
                          <div
                            className={styles.text1}
                            id={`bimUploadSituation${index}`}
                            style={{ animation: `uploadscroll${index} 5s linear infinite` }}
                          >
                            {item.name}
                          </div>
                          <div
                            className={styles.text2}
                            style={{ animation: `uploadscrollT${index} 5s linear infinite` }}
                          >
                            {item.name}
                          </div>
                        </div>
                      </Tooltip>
                    ) : (
                      <div className={styles.name}>{item.name}</div>
                    )}
                  </div>
                  <div className={styles.rightBox}>{item.value}</div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
};

export default BimProblemTrack;

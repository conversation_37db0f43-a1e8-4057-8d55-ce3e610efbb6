import React, { useState, useEffect } from "react";
import { Badge, Layout, Space, Typography, Carousel, Modal, Empty, Button } from 'antd';
import StylesNews from './index.less';
import { getPartitionStatisticsCountApi } from '@/services/epidemic';
import ReactEcharts from 'echarts-for-react';
import opts from '@/panel/echartOptions';
import More from "@/components/more";
import moment from 'moment'
// @ts-ignore
import Scrollbars from 'react-custom-scrollbars';
import Preview from '@/components/Preview'


interface NewsObj {
  id: number
  title: string
  gmt_modify: string
  logo: string
  picture: string
  pictures: any[]
  content: string
  userName: string
  source_type: number
  content_type: number
}

const { Content } = Layout;
export default () => {
  const [echartsOption, setechartsOption] = useState({
    xAxis: {
      type: 'category',
      data: ['mon']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        data: [1],
        type: 'bar',
        showBackground: false,
        itemStyle: {
          normal: {
            label:{
              show: true, //开启显示
              position: 'top', //在上方显示
              textStyle: { //数值样式
                color: 'black',
                fontSize: 16
              },
            },
            color: '#2ba9e3'
          }
        }
      }
    ]
  })
  const [showPanel, setshowPanel] = useState(false)
  const getMyOption = (valueData, xData) => {
    return {
      tooltip: {
        trigger: 'axis',
        formatter(params) {
          return `区域名称:${params[0].name} 人数:${params[0].value}`
        }
      },
      xAxis: {
        type: 'category',
        data: xData,
        axisLabel: {
          interval: 0,
          rotate: 20
      },
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          data: valueData,
          type: 'bar',
          showBackground: false,
          barWidth: 30,
          itemStyle: {
            normal: {
              label:{
                show: true, //开启显示
                position: 'top', //在上方显示
                textStyle: { //数值样式
                  color: 'black',
                  fontSize: 16
                },
              },
              color: '#2ba9e3'
            }
          }
        }
      ]
    }
  }


  useEffect(() => {
    getPartitionStatisticsCountApi().then((res: any) => {
      console.log(res, '得到的')
      if (res && res.result.length > 0) {
        setshowPanel(true)
        let valueArr = res.result.map(item => item.number)
        let xData = res.result.map(item => item.areaName)
        setechartsOption(getMyOption(valueArr, xData))
      }
    })
  }, [])


  return (
    <div>
      <More jumpUrl={`${window.location.origin}/console/person/#/person/home`}/>
      {
     
     showPanel? <Content className={StylesNews.mainCon}>
          <ReactEcharts
            opts={opts}
            option={echartsOption}
          />
        </Content>
        :<Empty image={Empty.PRESENTED_IMAGE_SIMPLE}/>
        }
    </div>

  );
}

// 创建线柱图
export const createDualAxesConfigs: any = (dataList: any[]) => {
  return {
    data: [dataList, dataList],
    xField: 'label',
    yField: ['column', 'line'],
    slider: {},
    legend: {
      position: 'top',
    },
    meta: {
      column: {
        alias: '检查项目数',
        formatter: (v: any) => {
          return v + '个';
        },
      },
      line: {
        alias: '检查覆盖率',
        formatter: (v: any) => {
          return v + '%';
        },
      },
    },
    geometryOptions: [
      {
        geometry: 'column',
        color: '#2478FF',
        minColumnWidth: 12,
        maxColumnWidth: 24,
      },
      {
        geometry: 'line',
        color: '#FFCD00',
        lineStyle: {
          lineWidth: 2,
        },
      },
    ],
    // interactions: [
    //   {
    //     type: 'tooltip',
    //     cfg: { start: [{ trigger: 'element:click' }] }
    //   }
    // ]
  };
};

// 创建分组条形图
export const createGroupBarConfigs: any = (data: any) => {
  let _data: any[] = [];
  for (let item of data) {
    _data = _data.concat([
      { label: item.orgName, type: '一次整改完成率', value: item.onceRectificationRate * 1 },
      { label: item.orgName, type: '整改率', value: item.rectificationRate * 1 },
    ]);
  }
  return {
    data: _data,
    isGroup: true,
    xField: 'value',
    yField: 'label',
    seriesField: 'type',
    marginRatio: 0,
    color: ['#FFCD00', '#2478FF'],
    legend: {
      position: 'top',
    },
    label: {
      // 可手动配置 label 数据标签位置
      position: 'right', // 'left', 'middle', 'right'
      offset: 4,
    },
    scrollbar: {
      type: 'vertical',
    },
    barStyle: {
      radius: [2, 2, 0, 0],
    },
    meta: {
      value: {
        formatter: (v: any) => `${v}%`,
      },
      label: {
        formatter: (v: any) => {
          return v && v.length > 5 ? v.slice(0, 5) + '...' : v;
        },
      },
    },
    // interactions: [
    //   {
    //     type: 'tooltip',
    //     cfg: { start: [{ trigger: 'element:click' }] }
    //   }
    // ]
  };
};

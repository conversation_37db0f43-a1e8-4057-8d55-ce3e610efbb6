// 隐患分析
import EmptyCenter from '@/components/EmptyCenter';
import opts from '@/panel/echartOptions';
import { useRequest } from 'ahooks';
import { Layout, Radio } from 'antd';
import ReactEcharts from 'echarts-for-react';
import React, { useMemo, useState } from 'react';
import { getHazardAnalysis } from '../api';

// 生成随机颜色的函数
const builtInColors = ['#2478FF', '#49C292', '#2FC0F9', '#575EFF'];
const generateHSLColor = (hue: number) => {
  return `hsl(${hue}, 70%, 50%)`;
};
const extraColors = Array.from({ length: Math.max(0, 30) }, (_, index) => {
  return generateHSLColor((index + builtInColors.length) * (360 / (30 + 1))); // 调整色相
});

const OPTIONS = [
  {
    label: '隐患类别',
    value: '1',
    color: [...builtInColors, ...extraColors],
  },
  {
    label: '隐患等级',
    value: '2',
    color: ['#FFCD00', '#FF9400', '#FF4D4F'],
  },
  {
    label: '隐患状态',
    value: '3',
    color: ['#2478FF', '#FFCD00', '#49C292'],
  },
];

const FutureRiskTrend = () => {
  const [selectType, setSelectType] = useState<'1' | '2' | '3'>('1');
  const { data = [] } = useRequest(
    async () => {
      const res = await getHazardAnalysis(selectType);
      return res?.data || [];
    },
    {
      refreshDeps: [selectType],
    },
  );

  return useMemo(() => {
    const echartsOption = {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)', // 显示占比百分数
      },
      title: {
        text: data.reduce((pre, cur) => {
          return pre + cur.count;
        }, 0),
        subtext: '隐患总数',
        top: '40%',
        left: '44%',
        textAlign: 'center',
        textStyle: {
          color: '#191919',
          fontSize: 24,
        },
        subtextStyle: {
          color: '#666',
          fontSize: 14,
        },
      },
      legend: {
        type: 'scroll',
        orient: 'vertical',
        top: 'center',
        right: '9%',
        icon: 'rect',
        itemWidth: 10, // 矩形宽度
        itemHeight: 10, // 矩形高度
      },
      color: OPTIONS[+selectType - 1].color, // 自定义颜色
      series: [
        {
          name: OPTIONS[+selectType - 1].label,
          type: 'pie',
          radius: ['50%', '70%'], // 内半径和外半径，形成环形图
          center: ['45%', '50%'],
          label: {
            show: false, // 隐藏连接线
          },
          data: data.map((item: any) => ({
            value: item.count,
            name: item.name,
          })),
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
            },
          },
        },
      ],
    };
    return (
      <Layout className="layoutPanelPm">
        <div style={{ position: 'absolute', right: 24, top: 16 }}>
          <Radio.Group
            size="small"
            options={OPTIONS}
            onChange={({ target: { value } }) => setSelectType(value)}
            defaultValue={selectType}
            optionType="button"
          />
        </div>
        {data ? (
          <Layout.Content>
            <ReactEcharts
              style={{ height: '98%' }}
              opts={opts}
              option={echartsOption}
              theme="clear"
            />
          </Layout.Content>
        ) : (
          <EmptyCenter description="暂无数据" />
        )}
      </Layout>
    );
  }, [selectType, data]);
};

export default FutureRiskTrend;

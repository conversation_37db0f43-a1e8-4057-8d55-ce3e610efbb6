import More from '@/components/more';
import { getCurrentUser } from '@/services/api';
import React from 'react';
import Company from './components/Company';
import Project from './components/Project';
function jumpPage(url: string) {
  window.location.replace(url);
}
export default () => {
  const currentUser = getCurrentUser();
  return (
    <>
      <More jumpUrl="/console/schedule-management/schedule" onClick={jumpPage} />
      {currentUser?.type === 1 ? <Company /> : <Project />}
    </>
  );
};

import { Bullet } from '@ant-design/plots';
import { Space } from 'antd';
import React, { useRef } from 'react';
const Index = ({
  data = [],
  handClick = (el: any, deptType: string) => {},
  deptType = 'control',
}) => {
  const dataRef: any = useRef(null);
  const deptTypeRef: any = useRef(null);
  dataRef.current = data;
  deptTypeRef.current = deptType;
  const config: any = {
    data,
    measureField: 'measures',
    rangeField: 'ranges',
    targetField: 'target',
    xField: 'title',
    color: {
      range: ['#FFbcb8', '#FFe0b0', '#bfeec8'],
      measure: '#5B8FF9',
      target: '#39a3f4',
    },
    size: {
      range: 15,
      measure: 10,
      target: 10,
    },
    label: {
      measure: {
        position: 'middle',
        style: {
          fill: '#fff',
        },
        formatter: (value: { measures: number }) => {
          // console.log(value)
          return value?.measures >= 0 ? value?.measures + '%' : '-';
        },
      },
    },
    tooltip: {
      customContent: (title: string) => {
        const el = dataRef.current?.find((item: { title: string }) => {
          return item?.title === title;
        });
        return (
          <>
            {deptTypeRef.current === 'control' ? (
              <div style={{ padding: '10px', lineHeight: '24px' }}>
                <div>{el?.title}</div>
                <div>
                  <Space>
                    <span className={`ant-badge-status-dot ant-badge-status-processing`} />
                    进度受控率：<span>{el?.rate}%</span>
                  </Space>
                </div>
                <div>
                  &nbsp;&nbsp;风险项目：{el?.warningLevelDTO?.totalCount}&nbsp;&nbsp;
                  {el?.rate >= 0 ? 100 - el?.rate : '-'}%
                </div>
                <div>
                  &nbsp;&nbsp;蓝色风险：{el?.warningLevelDTO?.blueWarningCount}&nbsp;&nbsp;
                  {el?.warningLevelDTO?.blueWarningRate}%
                </div>
                <div>
                  &nbsp;&nbsp;黄色风险：{el?.warningLevelDTO?.yellowWarningCount}&nbsp;&nbsp;
                  {el?.warningLevelDTO?.yellowWarningRate}%
                </div>
                <div>
                  &nbsp;&nbsp;红色风险：{el?.warningLevelDTO?.redWarningCount}&nbsp;&nbsp;
                  {el?.warningLevelDTO?.redWarningRate}%
                </div>
              </div>
            ) : (
              <div style={{ padding: '16px', lineHeight: '24px' }}>
                <div>{el?.title}</div>
                <div>
                  <Space>
                    <span className={`ant-badge-status-dot ant-badge-status-processing`} />
                    管控覆盖率：<span>{el?.rate}%</span>
                  </Space>
                </div>
                <div>&nbsp;&nbsp;已传计划项目数：{el?.sumTotal}个</div>
                <div>&nbsp;&nbsp;未传计划项目数：{el?.number}个</div>
              </div>
            )}
          </>
        );
      },
    },
    xAxis: {
      line: null,
    },
    yAxis: false,
    // 自定义 legend
    legend: {
      custom: true,
      position: 'bottom',
      items: [
        {
          value: '差',
          name: '差',
          marker: {
            symbol: 'square',
            style: {
              fill: '#FFbcb8',
              r: 5,
            },
          },
        },
        {
          value: '良',
          name: '良',
          marker: {
            symbol: 'square',
            style: {
              fill: '#FFe0b0',
              r: 5,
            },
          },
        },
        {
          value: '优',
          name: '优',
          marker: {
            symbol: 'square',
            style: {
              fill: '#bfeec8',
              r: 5,
            },
          },
        },
        {
          value: '实际值',
          name: '实际值',
          marker: {
            symbol: 'square',
            style: {
              fill: '#5B8FF9',
              r: 5,
            },
          },
        },
        {
          value: '目标值',
          name: '目标值',
          marker: {
            symbol: 'line',
            style: {
              stroke: '#39a3f4',
              r: 5,
            },
          },
        },
      ],
    },
  };
  const onReady = (plot: any) => {
    plot.on('element:click', (evt: any) => {
      handClick &&
        handClick(
          dataRef.current?.find((item: { title: string }) => {
            return item?.title === evt?.data?.data?.title;
          }),
          deptTypeRef.current,
        );
    });
  };
  return (
    <>
      <Bullet {...config} onReady={onReady} />
    </>
  );
};

export default Index;

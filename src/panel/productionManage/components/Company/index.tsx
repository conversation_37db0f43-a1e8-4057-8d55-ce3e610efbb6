import DescriptionsCard from '@/components/DescriptionsCard';
import {
  controlCoverage,
  deptScheduleControl,
  deptScheduleCoverage,
  scheduleControl,
} from '@/services/productionManage';
import { useRequest } from 'ahooks';
import { Col, Radio, Row, Space } from 'antd';
import React, { useEffect, useState } from 'react';
import Bullet from '../Company/Bullet';
export default () => {
  const [deptType, setDeptType] = useState('control');
  const type = 'interior';
  const { data: deptScheduleControlData, run: runDeptScheduleControl } = useRequest(
    () => {
      return deptScheduleControl({
        type,
        projectStatus: 1,
      });
    },
    { manual: true },
  );

  const { data: deptScheduleCoverageData, run: runDeptScheduleCoverage } = useRequest(
    () => {
      return deptScheduleCoverage({
        type,
        projectStatus: 1,
      });
    },
    { manual: true },
  );

  const { data: controlCoverageData, run: runControlCoverage } = useRequest(
    () => {
      return controlCoverage({
        type,
        projectStatus: 1,
      });
    },
    { manual: true },
  );

  const { data: scheduleControlData, run: runScheduleControl } = useRequest(
    () => {
      return scheduleControl({
        type,
        projectStatus: 1,
      });
    },
    { manual: true },
  );
  const formatter = (value) => {
    let res = value;
    if (res.length > 8) {
      res = res.substring(0, 8) + '...';
    }
    return res;
  };
  useEffect(() => {
    runDeptScheduleControl();
    runDeptScheduleCoverage();
    runControlCoverage();
    runScheduleControl();
  }, []);

  const handleDeptTypeChange = (e) => {
    setDeptType(e?.target?.value);
  };
  const extraDeptStatistic = () => {
    return (
      <>
        <Space>
          <Radio.Group
            defaultValue="control"
            buttonStyle="solid"
            onChange={handleDeptTypeChange}
            size="small"
          >
            <Radio.Button value="control">
              <span style={{ fontSize: '12px' }}>按受控排行</span>
            </Radio.Button>
            <Radio.Button value="coverage">
              <span style={{ fontSize: '12px' }}>按覆盖排行</span>
            </Radio.Button>
          </Radio.Group>
        </Space>
      </>
    );
  };
  return (
    <section>
      <DescriptionsCard
        data={[
          { label: '项目总数', value: controlCoverageData?.data?.sumTotal ?? '--' },
          {
            label: '管控覆盖率',
            value:
              controlCoverageData?.data?.rate >= 0 ? controlCoverageData?.data?.rate + '%' : '--',
          },
          {
            label: '进度受控率',
            value:
              scheduleControlData?.data?.rate >= 0 ? scheduleControlData?.data?.rate + '%' : '--',
          },
        ]}
      />
      <Row justify="space-between">
        <Col>
          <Space>{`企业管控排行`}</Space>
        </Col>
        <Col>{extraDeptStatistic()}</Col>
      </Row>
      <div style={{ height: '180px', overflowY: 'auto', paddingTop: '10px' }}>
        <div
          style={{
            height:
              deptType === 'control'
                ? deptScheduleControlData?.data?.length
                  ? 25 * (deptScheduleControlData?.data?.length + 1) + 'px'
                  : '0'
                : deptScheduleCoverageData?.data?.length
                ? 25 * (deptScheduleCoverageData?.data?.length + 1) + 'px'
                : '0',
          }}
        >
          {
            <Bullet
              data={
                deptType === 'control'
                  ? deptScheduleControlData?.data?.map((item) => {
                      return {
                        title: formatter(item?.deptName),
                        ranges: [50, 80, 100],
                        measures: item?.rate,
                        target: 100,
                        id: item?.deptId,
                        ...item,
                      };
                    })
                  : deptScheduleCoverageData?.data?.map((item) => {
                      return {
                        title: formatter(item?.deptName),
                        ranges: [50, 80, 100],
                        measures: item?.rate,
                        target: 100,
                        id: item?.deptId,
                        ...item,
                      };
                    })
              }
              deptType={deptType}
            />
          }
        </div>
      </div>
    </section>
  );
};

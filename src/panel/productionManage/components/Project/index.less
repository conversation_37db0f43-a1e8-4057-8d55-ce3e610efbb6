.progressShowCard {
  :global {
    .ant-pro-card-body {
      padding-bottom: 0;
    }
  }
}

.projectTitleBox {
  width: 0;
  white-space: nowrap;
}

.projectTitle {
  overflow: hidden;
  text-overflow: ellipsis;
}

.stepBox {
  flex: 1;
  overflow: hidden;
  margin: 0 16px;
}

.stepWarpper {
  display: flex;

  :global {
    .ant-steps-item-title {
      overflow: hidden;
      width: 100%;
      height: 26px;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .ant-steps-item-description {
      font-size: 12px !important;
    }

    // .ant-steps {
    //   margin-left: -20px;
    // }

    .ant-steps-item {
      padding-top: 0 !important;
    }

    .ant-steps-item-process .ant-steps-item-icon .ant-steps-icon {
      color: #fff !important;
    }
  }
}

.buildWarpper {
  display: flex;
  align-items: center;
}

.icon1 {
  width: 48px;
  height: 48px;
}

.infoShowWraper {
  :global {
    .ant-pro-card-body {
      padding: 0;
    }
  }

  .leftCircle {
    font-size: 24px;
    color: rgba(0, 0, 0, 0.65);
    padding-right: 10px;
    z-index: 1;
  }

  .rightCircle {
    font-size: 24px;
    color: rgba(0, 0, 0, 0.65);
    padding-left: 10px;
    z-index: 1;
  }
}

.infoShowBox {
  border-right: 1px solid #F0F0F0;
  border-bottom: 1px solid #F0F0F0;
}

:global {
  .ant-pro-card-title {
    font-weight: bold;
  }
}

.totalProgress {
  :global {
    .ant-pro-statistic-card-content-horizontal {
      justify-content: flex-start;
    }
  }
}
import DescriptionsCard from '@/components/DescriptionsCard';
import EmptyCenter from '@/components/EmptyCenter';
import { getCurrentUser } from '@/services/api';
import { getAuditFinishInfoList, getOverallPlanDetail } from '@/services/productionManage';
import { useRequest } from 'ahooks';
import { Row, Select, Space, Spin } from 'antd';
import React, { useEffect, useState } from 'react';
import Step from './Step';
const Index = () => {
  const planType = 'overall';
  const type = 'interior';
  const currentUser = getCurrentUser();
  const [selectInfo, setSelectInfo] = useState<any>({});
  const { data: infoShowDetailData, run: runInfoShowDetailData } = useRequest(
    () => {
      return getOverallPlanDetail({
        type,
        infoId: selectInfo?.id,
      });
    },
    { manual: true },
  );

  const {
    data: infoListData,
    loading,
    run: runInfoListData,
  } = useRequest(
    () => {
      return getAuditFinishInfoList({
        type,
        planType: planType,
        projectIdList: [currentUser?.pjId],
      });
    },
    {
      manual: true,
    },
  );

  const handleInfoListChange = (val) => {
    const info = infoListData?.data?.find((item) => {
      return item?.id === val?.value;
    });
    setSelectInfo({
      ...info,
      infoId: info?.id,
    });
  };

  useEffect(() => {
    if (infoListData?.data?.length > 0) {
      setSelectInfo(infoListData?.data?.[0]);
    } else {
      setSelectInfo(null);
    }
  }, [infoListData]);

  useEffect(() => {
    runInfoListData();
  }, []);

  useEffect(() => {
    if (selectInfo && selectInfo?.id) {
      runInfoShowDetailData();
    }
  }, [selectInfo]);

  return (
    <Spin spinning={loading}>
      <section>
        {infoListData?.data && infoListData?.data?.length > 0 ? (
          <>
            <Row gutter={0}>
              <Space>
                总计划：
                <Select
                  options={infoListData?.data?.map((item) => {
                    return {
                      value: item?.id,
                      label: item?.name,
                    };
                  })}
                  labelInValue={true}
                  style={{ width: '120px' }}
                  onChange={handleInfoListChange}
                  value={{ value: selectInfo?.id, name: selectInfo?.name }}
                />
              </Space>
            </Row>
            <DescriptionsCard
              style={{ paddingTop: '10px' }}
              data={[
                { label: '计划工期', value: `${infoShowDetailData?.data?.planDuration ?? '--'}天` },
                {
                  label: '执行工期',
                  value: `${infoShowDetailData?.data?.executeDuration ?? '--'}天`,
                },
                {
                  label: '剩余工期',
                  value: `${infoShowDetailData?.data?.residueDuration ?? '--'}天`,
                },
              ]}
            />
            <Step milestoneList={infoShowDetailData?.data?.milestoneList ?? []} />
          </>
        ) : (
          <div style={{ paddingTop: '150px' }}>
            <EmptyCenter description="暂无数据" />
          </div>
        )}
      </section>
    </Spin>
  );
};

export default Index;

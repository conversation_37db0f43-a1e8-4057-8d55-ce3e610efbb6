import LeftCircleOutlined from '@/assets/left.png';
import RightCircleOutlined from '@/assets/right.png';
import { Empty, Image, Steps } from 'antd';
import React, { useRef } from 'react';
import styles from './index.less';
const { Step } = Steps;
export default (props) => {
  const { milestoneList } = props;
  const curEleRef = useRef(null);
  const current = milestoneList?.findIndex((item, index) => item?.isCurrentMilestone);
  const handleScrollRight = () => {
    curEleRef.current.scrollLeft += curEleRef?.current?.clientWidth;
  };

  const handleScrollLeft = () => {
    curEleRef.current.scrollLeft -= curEleRef?.current?.clientWidth;
  };
  const statusMap = {
    unstart: 'wait',
    under: 'process',
    finish: 'finish',
    stop: 'error',
  };
  return (
    <>
      {milestoneList?.length > 0 ? (
        <div style={{ position: 'relative', width: '100%' }}>
          <div style={{ top: '60px', left: '-5px', position: 'absolute' }}>
            <div style={{ fontSize: '12px', marginTop: '16px' }}>计划完成</div>
            <div style={{ fontSize: '12px', marginTop: '19px' }}>实际完成</div>
          </div>

          <div className={styles?.stepWarpper}>
            <Image src={LeftCircleOutlined} onClick={handleScrollLeft} preview={false} width={30} />
            <div className={styles?.stepBox} ref={curEleRef}>
              <Steps current={current} labelPlacement="vertical" size="small">
                {milestoneList?.map((item, index) => {
                  return (
                    <Step
                      key={index}
                      status={statusMap[item?.status]}
                      title={item?.name}
                      description={
                        <>
                          <br />
                          {item?.planEndDate}
                          <br />
                          <br />
                          <span
                            style={
                              item?.isOverDue && item?.isOverDue !== '0' ? { color: '#FAAD14' } : {}}
                          >
                            {item?.actualEndTime ?? '--'}
                          </span>
                        </>
                      }
                    />
                  );
                })}
              </Steps>
            </div>
            <Image
              src={RightCircleOutlined}
              onClick={handleScrollRight}
              preview={false}
              height={30}
            />
          </div>
        </div>
      ) : (
        <Empty imageStyle={{ height: '140px' }} />
      )}
    </>
  );
};

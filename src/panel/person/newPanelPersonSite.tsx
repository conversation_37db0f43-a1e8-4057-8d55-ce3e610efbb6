import React, { useState } from "react";
import { Layout, Radio, Space } from 'antd';
import StylesAttendanceRank from './index.less';
import { getAttendanceWorkerRank } from '@/services/person';
import EmptyCenter from '@/components/EmptyCenter';
import { useMount, useUpdateEffect } from "react-use";
import More from "@/components/more";
// @ts-ignore
import Scrollbars from 'react-custom-scrollbars';

const { Content } = Layout;
export default () => {
  const optionsDate = [
    { label: '按参建单位', value: 1 },
    { label: '按工种', value: 2 },
  ];
  const optionsOrder = [
    { label: '降序', value: 2 },
    { label: '升序', value: 1 },
  ];
  const [dateType, setDateType] = useState(1);
  const [orderBy, setOrderBy] = useState(1);
  const [dateDate, setDateDate] = useState<any[]>([]);
  const handleGetCompanyAttendanceRank = async () => {
    let rankData: any = {
      rankType:dateType,
      sortType:orderBy
    };
    let data = await getAttendanceWorkerRank(rankData);
    data && setDateDate(data.result);
  };
  const renderThumb = (payload: any) => {   // 设置滚动条的样式
    const thumbStyle = {
      backgroundColor: '#000',
      borderRadius: '6px',
      opacity: 0,
      ...payload
    }
    return <div style={{ ...thumbStyle }} />
  };
  useUpdateEffect(() => {
    handleGetCompanyAttendanceRank()
  }, [dateType, orderBy])
  useMount(() => {
    handleGetCompanyAttendanceRank()
  })
  return (
    <Layout className='layoutPanelPm'>
      <More jumpUrl={`${window.location.origin}/console/rysmz_project_person_analyse/PresencePerson`} />
      <div style={{ position: 'absolute', top: 13, right: 65 }}>
        <Space>
          <Radio.Group
            size='small'
            options={optionsDate}
            onChange={({ target: { value } }) => setDateType(value)}
            defaultValue={dateType}
            optionType="button"
            buttonStyle="solid"
          />
          <Radio.Group
            size='small'
            options={optionsOrder}
            onChange={({ target: { value } }) => setOrderBy(value)}
            defaultValue={orderBy}
            optionType="button"
            buttonStyle="solid"
          />
        </Space>
      </div>
      <Content>
        <Scrollbars renderThumbVertical={() => renderThumb({ right: '4px', width: '8px' })}>
          {
            dateDate.length > 0 ? dateDate.map(item => {
              return <div key={item.projectId} className={StylesAttendanceRank.attendanceRank}>
                <div>{item.name}</div>
                <div>{item.sceneWorkerCnt}人</div>
              </div>
            }) : <EmptyCenter description='暂无数据' />
          }
        </Scrollbars>
      </Content>
    </Layout>
  )
}

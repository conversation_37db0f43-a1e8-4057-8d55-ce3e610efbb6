import React, {useState} from "react";
import { Layout, Radio, Space  } from 'antd';
import StylesAttendanceRank from './index.less';
import {getCompanyAttendanceRank, getWeekAttendanceAvg} from '@/services/person';
import EmptyCenter from '@/components/EmptyCenter';
import {useMount, useUpdateEffect} from "react-use";
import More from "@/components/more";
// @ts-ignore
import Scrollbars from 'react-custom-scrollbars';
import defaultSetting from '@/defaultSetting';

const { Content } = Layout;
export default () => {
  const optionsDate = [
    { label: '今日', value: 1 },
    { label: '7日日均', value: 2 },
  ];
  const optionsOrder = [
    { label: '降序', value: 2 },
    { label: '升序', value: 1 },
  ];
  const [dateType, setDateType] = useState(1);
  const [orderBy, setOrderBy] = useState(2);
  const [dateDate, setDateDate] = useState<any[]>([]);
  const handleGetCompanyAttendanceRank = async () => {
    let data: any[] = [];
    if (dateType === 1) {
      data = await getCompanyAttendanceRank(orderBy);
    }
    if (dateType === 2) {
      data = await getWeekAttendanceAvg(orderBy);
    }
    data && setDateDate(data);
  }
  const renderThumb = (payload: any) => {   // 设置滚动条的样式
    const thumbStyle = {
      backgroundColor: '#000',
      borderRadius: '6px',
      opacity: 0,
      ...payload
    }
    return <div style={{...thumbStyle }}/>
  };
  useUpdateEffect(() => {
    handleGetCompanyAttendanceRank().then()
  }, [dateType, orderBy])
  useMount(() => {
    handleGetCompanyAttendanceRank().then()
  })
  return (
    <Layout className='layoutPanelPm'>
      <More jumpUrl={`${defaultSetting.routes.console}corPerson/attendance`} />
      <div style={{position: 'absolute', top: 15, right: 65}}>
        <Space>
          <Radio.Group
            size='small'
            options={optionsDate}
            onChange={({target: {value}}) => setDateType(value)}
            defaultValue={dateType}
            optionType="button"
            buttonStyle="solid"
          />
          <Radio.Group
            size='small'
            options={optionsOrder}
            onChange={({target: {value}}) => setOrderBy(value)}
            defaultValue={orderBy}
            optionType="button"
            buttonStyle="solid"
          />
        </Space>
      </div>
      <Content>
        <Scrollbars renderThumbVertical={() => renderThumb({ right: '4px',  width: '8px'})}>
          {
            dateDate.length > 0 ? dateDate.map(item => {
              return <div key={item.projectId} className={StylesAttendanceRank.attendanceRank}>
                <div>{item.projectName}</div>
                <div>{item.attendanceRate}%</div>
              </div>
            }) : <EmptyCenter description='暂无数据' />
          }
        </Scrollbars>
      </Content>
    </Layout>
  )
}

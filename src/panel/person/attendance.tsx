import React, {useState} from "react";
import ReactEcharts from 'echarts-for-react';
import { Layout } from 'antd';
import DescriptionsCard from '@/components/DescriptionsCard';
import opts, {axis, title, legend, color, grid} from '@/panel/echartOptions';
import {getWeekAttendance, getHomeCount} from '@/services/person';
import {useMount} from 'react-use';
import More from "@/components/more";
import {getCurrentUser} from '@/services/api';
import defaultSetting from "@/defaultSetting";

const { Header,Content } = Layout;
export default () => {
  const currentUser = getCurrentUser();
  const [homeCount ,setHomeCount] = useState<any[]>([{label: '现场(人)', value: 0}]);
  const [echartsOption, setEchartsOption] = useState<any>({
    title: {
      text: '近7日出勤率趋势',
      ...title,
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
      }
    },
    legend: {
      ...legend,
      data: ['全部', '建筑工人', '管理人员']
    },
    grid,
    color,
    xAxis: [
      {
        type: 'category',
        ...axis,
      }
    ],
    yAxis: [
      {
        type: 'value',
        ...axis,
      }
    ],
    series: []
  });
  useMount(() => {
    getWeekAttendance().then((res: any) => {
      const date: any = []
      const data: any = []
      res?.forEach?.((item: any) => {
        date.push(item.date)
        data.push(item.attendanceRate)
      })
      setEchartsOption({
        ...echartsOption,
        xAxis: [
          {
            type: 'category',
            boundaryGap: false,
            ...axis,
            data: date
          }
        ],
        series: [
          {
            name: '出勤率',
            type: 'line',
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [{
                  offset: 0, color: 'rgba(0,192,131,0.5)' // 0% 处的颜色
                }, {
                  offset: 1, color: 'rgba(0,192,131,0)' // 100% 处的颜色
                }],
                global: false // 缺省为 false
              }
            },
            data
          }
        ]
      })
    });
    getHomeCount().then((res: any) => {
      const value = currentUser.type === 1 ? res?.projectCount : res?.sceneCount
      setHomeCount([
        {label: currentUser.type === 1 ? '活跃项目(个)' : '现场(人)', value: value || '--'},
        {label: '今日出勤(人)', value: res?.attendanceCount || '--'},
        {label: '在场(人)', value: res?.workerCount || '--'},
        {label: '今日出勤率(%)', value: res?.attendanceRate ? `${res?.attendanceRate}%` : '--'},
      ])
    })
  })
  return (
    <Layout className='layoutPanelPm'>
      <More jumpUrl={currentUser.type === 1 ? `${defaultSetting.routes.console}corPerson/attendance` : `${defaultSetting.routes.console}person/#/person/home`} />
      <Header>
        <DescriptionsCard data={homeCount}/>
      </Header>
      <Content>
        <ReactEcharts
          style={{height: '100%'}}
          opts={opts}
          option={echartsOption}
          theme="clear"
        />
      </Content>
    </Layout>
  )
}

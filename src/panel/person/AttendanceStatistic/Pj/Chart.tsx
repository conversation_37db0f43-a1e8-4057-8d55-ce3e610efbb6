import React, { useMemo } from 'react';
import ReactECharts from 'echarts-for-react';
import moment from 'moment';

const chartColor = 'rgba(0,0,0,0.45)';
const chartLine = '#e8e8e8';
export const dark = window?.__PMS_CONSOLE__?.config?.dark ?? false; // 是否开启暗黑模式
export const getDarkValue = (defaultValue: any, darkValue: any) => dark ? darkValue : defaultValue;

interface YData {
  name: string;
  color: string;
  data: number[];
}

export interface IChartData {
  xData: string[];
  yDataLeft: YData[];
  yDataRight: YData[];
}

interface IProps {
  loading?: boolean;

  yLeftName?: string;
  yRightName?: string;
  chartData?: IChartData;

  // 是否显示滚动条
  slider?: boolean;
  // 滚动条显示数据个数
  sliderSize?: number;
  // 滚动条默认显示末尾的数据
  isSliderFromEnd?: boolean;
}
export default function MultipleColumnChart(props: IProps) {
  const { yLeftName, yRightName, slider = true, sliderSize = 7, isSliderFromEnd } = props;

  function getMax(max: number): { min: number; max: number; interval: number; padding: number } {
    const padding = Math.max(30, `${max || 0}`.length * 10);
    let base = Math.floor(max / 25);
    let less = max % 25;
    max = Math.max(less ? (base + 1) * 25 : max, 100);

    return {
      max,
      min: 0,
      padding,
      interval: Math.floor(max / 5),
    };
  }

  const option = useMemo(() => {
    const barWidth = 10;
    const xData = props?.chartData?.xData || [];
    const yDataLeftList = props?.chartData?.yDataLeft || [];
    const yDataRightList = props?.chartData?.yDataRight || [];

    const maxNum = Math.max(
      100,
      ...yDataLeftList.map((x) => x.data).flat(),
    );

    const maxNumRight = Math.max(
      100,
      ...yDataRightList.map((x) => x.data).flat(),
    );

    const { min, max, interval, padding } = getMax(maxNum);
    const { min: minRight, max: maxRight, interval: intervalRight, padding: paddingRight } = getMax(maxNumRight);

    const config: any = {
      legend: {
        itemWidth: 12,
        itemHeight: 12,
        top: 20,
        itemGap: 48,
        textStyle: {
          color: getDarkValue('rgba(0,0,0,0.65)', chartColor),
        },
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        backgroundColor: 'rgba(0,0,0,0.65)',
        formatter: function (params: any) {
          const list = params.map((x: any) => {
            const isLeft = x.seriesType === 'bar'
            return `<div>${x.marker}${x.seriesName}：${x.value} ${isLeft ? yLeftName : yRightName}</div>`
          })
          return `
                  <div style="color: white;">
                   <div>${params[0].axisValueLabel}</div>
                   ${list.join('')}
                  </div>`;
        },
      },
      grid: {
        left: padding,
        right: paddingRight,
        bottom: 32,
      },
      xAxis: {
        type: 'category',
        data: xData,
        axisTick: {
          show: false,
        },
        axisLabel: {
          color: chartColor,
        },
        axisLine: {
          lineStyle: {
            color: chartLine,
          },
        },
        axisPointer: {
          label: {
            color: 'white',
            backgroundColor: 'rgba(0,0,0,0.6)',
          },
        },
      },
      yAxis: [
        {
          name: yLeftName,
          type: 'value',
          nameTextStyle: {
            padding: [0, 24, 8, 0],
            color: chartColor,
          },
          axisLabel: {
            color: chartColor,
          },
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false
          },
          min,
          max,
          interval,
        },
        {
          name: yRightName,
          type: 'value',
          nameTextStyle: {
            padding: [0, -40, 8, 0],
            color: chartColor,
          },
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            fontSize: 12,
            color: chartColor,
          },
          min: minRight,
          max: maxRight,
          interval: intervalRight,
          splitLine: {
            lineStyle: {
              color: chartLine
            },
          },
        },
      ],
      series: [
        ...yDataLeftList.map((x) => {
          return {
            barWidth,
            name: x.name,
            color: x.color,
            data: x.data,
            type: 'bar',
            showSymbol: false,
          };
        }),
        ...yDataRightList.map((x) => {
          return {
            symbol: false,
            name: x.name,
            color: x.color,
            data: x.data,
            type: 'line',
            showSymbol: false,
            yAxisIndex: 1,
          };
        }),
      ],
    };

    if (slider) {
      const addition = sliderSize - 1;
      const start = isSliderFromEnd ? xData.length - addition : 0;
      const end = start + addition;
      config['dataZoom'] = [
        {
          type: 'slider',
          orient: 'horizontal',
          show: true, //控制滚动条显示隐藏
          realtime: true, //拖动滚动条时是否动态的更新图表数据
          height: 8, //滚动条高度
          startValue: start,
          endValue: end,
          bottom: 4,
          dataBackground: {
            lineStyle: {
              color: 'transparent',
            },
            areaStyle: {
              color: 'transparent',
            },
          },
          handleSize: 10,
          handleStyle: {
            color: '#E5E5E5',
          },
          borderColor: 'transparent',
          textStyle: {
            color: 'transparent',
          },
          fillerColor: '#E5E5E5',
        },
      ];
    }

    return config;
  }, [props?.chartData]);

  return (
    <div style={{ width: '100%', height: '100%' }}>
      <ReactECharts
        showLoading={props?.loading}
        option={option}
        style={{ width: '100%', height: '100%' }}
        lazyUpdate={false}
        notMerge={false}
      />
    </div>
  );
}

// 测试用数据
export const demoChartData: IChartData = (() => {
  const count = 30;
  const label = moment().format('YYYY-MM-');
  const xData: string[] = [];
  const yData1: any = {
    name: '覆盖率',
    color: '#2478FF',
    data: [],
  };
  const yData2: any = {
    name: '合格率',
    color: '#49C292',
    data: [],
  };

  const yData3: any = {
    name: '出勤率',
    color: '#FFCD00',
    data: [],
  }

  for (let i: number = 1; i <= count; i++) {
    const v = i * 3;
    xData.push(label + (i < 10 ? `0${i}` : i));
    yData1.data.push(v);
    yData2.data.push(v + 100);
    yData3.data.push(v);
  }

  return {
    xData,
    yDataLeft: [yData1, yData2],
    yDataRight: [yData3],
  };
})();

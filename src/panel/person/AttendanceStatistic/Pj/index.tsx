import React, { useEffect, useState } from 'react';
import { Layout } from 'antd';
import More from "@/components/more";
import {getCurrentUser} from '@/services/api';
import defaultSetting from "@/defaultSetting";
import CountUp from "react-countup";
import styles from './index.less';
import ITitle from '@/common/ITitle';
import MultipleColumnChart, {demoChartData} from './Chart';
import * as api from '@/services/corPerson';
import { updateStatistic } from '@/services/corPerson';
import { spin } from '@/panel/person/AttendanceStatistic/Co/_utils';

interface IData {
  key?: string; // key
  value?: number; // 值
  title?: string; // 标题
  unit?: string;  // 单位
  decimals?: number;  // 精度
}

/**
 * 出勤统计
 */
const { Header,Content } = Layout;
export default () => {
  const [spinLoading, setSpinLoading] = useState(false);
  const [loading, setLoading] = useState<boolean>(false)
  const [chartData, setChartData] = useState<any>(undefined);
  const [list, setList] = useState<IData[]>([
    {key: 'presenceCnt', title: '现场人数', value: 0, unit: '人'},
    {key: 'attendanceCnt', title: '出勤人数', value: 0, unit: '人'},
    {key: 'workerCnt', title: '在场人数', value: 0, unit: '人'},
    {key: 'attendanceRate', title: '出勤率', value: 0, unit: '%', decimals: 2},
  ]);

  function fetchData () {
    setLoading(true);
    api.pjLatest30DaysTrends().then((result = []) => {
      setLoading(false);
      const xData: string[] = [];
      const yData1: any = {
        name: '在场人数',
        color: '#2478FF',
        data: [],
      };
      const yData2: any = {
        name: '出勤人数',
        color: '#49C292',
        data: [],
      };

      const yData3: any = {
        name: '出勤率',
        color: '#FFCD00',
        data: [],
      }

      result.forEach((x: any) => {
        xData.push(x?.recordDate ?? '');
        yData1.data.push(x?.presentCount ?? 0);
        yData2.data.push(x?.attendanceCount ?? 0);
        yData3.data.push(x?.attendanceRate ?? 0);
      })

      setChartData({
        xData,
        yDataLeft: [yData2, yData1],
        yDataRight: [yData3],
      })
    })
  }

  function fetchStatistic () {
    api.pjStatistic().then(({ result = {} }: any) => {
      setList(list.map(x => {
        return {
          ...x,
          value: result?.[x?.key || ''] || 0
        }
      }))
    })
  }

  useEffect(() => {
    setSpinLoading(true);
    setSpinLoading(false);
    fetchStatistic()
    fetchData()
  }, [])

  return (
    <Layout className='layoutPanelPm'>
      <More jumpUrl={`${defaultSetting.routes.console}rysmz_project_person_attendance/dailyReport`} />
      <Header className={styles.head}>
        {
          list.map(x => {
            return (
              <div key={x?.key} className={styles['head-item']}>
                <div className={styles.title}>{x?.title}</div>
                <div>
                  <CountUp
                    start={0}
                    end={x?.value || 0}
                    duration={2.75}
                    separator={','}
                    decimals={x?.decimals || 0}
                    delay={0}
                    decimal="."
                    prefix={''}
                    suffix=""
                  >
                    {({ countUpRef }: any) => (
                      <span ref={countUpRef} className={styles.count}/>
                    )}
                  </CountUp>
                  {x?.unit && <span className={styles.title} style={{marginLeft: 8}}>{x?.unit}</span>}
                </div>
              </div>
            )
          })
        }
      </Header>
      <Content style={{overflow: 'hidden', height: '100%', width: '100%', display: 'flex', flexDirection: 'column'}}>
        <ITitle
          wrapperStyle={{padding: 0}}
          title={<b>近30日出勤趋势</b>}
        />
        <div style={{width: '100%', flex: 1}}>
          <MultipleColumnChart
            isSliderFromEnd={true}
            loading={loading}
            chartData={chartData}
            yLeftName={'人'}
            yRightName={'%'}
            sliderSize={7}
          />
        </div>
        {spinLoading && spin}
      </Content>
    </Layout>
  )
}

import React, { useEffect, useState } from 'react';
import { Layout, Spin, Table } from 'antd';
import More from "@/components/more";
import {getCurrentUser} from '@/services/api';
import defaultSetting from "@/defaultSetting";
import CountUp from "react-countup";
import styles from './index.less';
import ITitle from '@/common/ITitle';
import ICustomSelect from '@/common/ICustomSelect';
import * as api from '@/services/corPerson';
import moment from 'moment';
import { useEffectOnce, useUpdateEffect } from 'react-use';
import { cloneDeep } from 'lodash';
import { updateStatistic } from '@/services/corPerson';
import { spin } from '@/panel/person/AttendanceStatistic/Co/_utils';

interface IData {
  key?: string; // key
  value?: number; // 值
  title?: string; // 标题
  unit?: string;  // 单位
  decimals?: number;  // 精度
}

/**
 * 出勤统计
 */
const width = 150;
const { Header,Content } = Layout;
const currentUser = getCurrentUser();
const isDepartment = currentUser.type === 1 && currentUser.currentDepartmentId;
export default () => {
  const [sortInfo, setSortInfo] = useState<{
    columnKey: string;
    sort: 'ascend' | 'default' | 'descend'
  }>({
    columnKey: 'avgAttendanceRate',
    sort: 'descend'
  })
  const [spinLoading, setSpinLoading] = useState(false);
  const [loading, setLoading] = useState<boolean>(false)
  const [filter, setFilter] = useState<{
    rate?: string;
  }>({
    rate: '60'
  })
  const [dataSource, setDatasource] = useState<any[]>([]);
  const [dataSourceCopy, setDatasourceCopy] = useState<any[]>([]);
  const [list, setList] = useState<IData[]>([
    {key: 'workerCount', title: '当日在场', value: 0, unit: '人'},
    {key: 'attendanceCount', title: '当日出勤', value: 0, unit: '人'},
    {key: 'attendanceRate', title: '当日出勤率', value: 0, unit: '%', decimals: 2},
    {key: 'complianceRate', title: '当日项目出勤达标率', value: 0, unit: '%', decimals: 2},
  ]);

  const columns: any[] = [
    {dataIndex: 'index', title: '排名', width: 80, ellipsis: true},
    {dataIndex: 'name', title: isDepartment ? '项目名称' : '组织名称', width, ellipsis: true},
    {dataIndex: 'number', title: isDepartment ? '参建单位总数' : '项目总数', width, ellipsis: true},
    {dataIndex: 'avgAttendanceRate', title: '平均出勤率', width, ellipsis: true,
      sorter: true,
      render: (v: any) => `${v ?? 0}%`,
      sortOrder: sortInfo.columnKey === 'avgAttendanceRate' ? sortInfo.sort : undefined
    },
    {dataIndex: 'complianceRate', title: '出勤达标率', width, ellipsis: true,
      sorter: true,
      render: (v: any) => `${v ?? 0}%`,
      sortOrder: sortInfo.columnKey === 'complianceRate' ? sortInfo.sort : undefined
    },
    {dataIndex: 'noComplianceNum', title: '不达标数', width, ellipsis: true},
    {dataIndex: 'complianceNum', title: '达标数', width, ellipsis: true},
  ]

  function getParams () {
    return {
      companyId: currentUser.coId,
      departmentId: currentUser.currentDepartmentId,
      projectStatus: 1, // 1-进行中(在建),2-完成(完工),5-筹备,6-立项,7-停工(这里传1)
      recordDate: moment().format('YYYY-MM-DD'),
      complianceRate: filter.rate
    }
  }

  function fetchData () {
    setLoading(true);
    const params = {
      ...getParams(),
      page: 1,
      pageSize: 99999
    };

    (isDepartment
        ? api.departmentRankList
        : api.coRankList
    )(params).then((res: any) => {
      const result = (isDepartment ? res?.dataList : res) || []
      setLoading(false);
      const dataSource = result?.map((x: any, index: number) => {
        return {
          ...x,
          index: index + 1
        }
      });
      const dataSourceCopy = cloneDeep(dataSource)
      setDatasource(sortDataSource(dataSource));
      setDatasourceCopy(dataSourceCopy);
    })
  }

  function fetchStatistic () {
    api.coStatistic(getParams()).then((result = {}) => {
      setList(list.map(x => {
        return {
          ...x,
          value: result[x?.key || ''] || 0
        }
      }))
    })
  }

  function sortDataSource (dataSource: any[]) {
    if (sortInfo.sort === 'default') return dataSource;
    return dataSource
      .sort((x: any, y: any) =>
        sortInfo.sort === 'descend'
          ? (y[sortInfo.columnKey] || 0) - (x[sortInfo.columnKey] || 0)
          : (x[sortInfo.columnKey] || 0) - (y[sortInfo.columnKey] || 0)
      ).map((x: any, index: number) => ({
        ...x,
        index: index + 1
      }))
  }

  useUpdateEffect(fetchData, [filter])

  useEffect(() => {
    setSpinLoading(true);
    setSpinLoading(false);
    fetchStatistic();
    fetchData();
  }, [])

  useUpdateEffect(() => {
    setDatasource(sortDataSource(cloneDeep(dataSourceCopy))) // 本地排序
  }, [sortInfo])

  return (
    <Layout className='layoutPanelPm'>
      <More jumpUrl={`${defaultSetting.routes.console}person-company-attendance/analysis`} />
      <Header className={styles.head}>
        {
          list.map(x => {
            return (
              <div key={x?.key} className={styles['head-item']}>
                <div className={styles.title}>{x?.title}</div>
                <div>
                  <CountUp
                    start={0}
                    end={x?.value || 0}
                    duration={2.75}
                    separator={','}
                    decimals={x?.decimals || 0}
                    delay={0}
                    decimal="."
                    prefix={''}
                    suffix=""
                  >
                    {({ countUpRef }: any) => (
                      <span ref={countUpRef} className={styles.count}/>
                    )}
                  </CountUp>
                  {x?.unit && <span className={styles.title} style={{marginLeft: 8}}>{x?.unit}</span>}
                </div>
              </div>
            )
          })
        }
      </Header>
      <Content style={{overflowY: 'hidden', display: 'flex', flexDirection: 'column', width: '100%'}}>
        <ITitle
          wrapperStyle={{padding: '12px 0 16px'}}
          title={<b>出勤排行</b>}
          titleRight={<div style={{marginTop: -6}}>
            出勤率达标线：
            <ICustomSelect
              value={filter.rate}
              style={{width: 150}}
              requestFn={() => Promise.resolve(
                [60, 65, 70, 75, 80, 85, 90, 95, 100].map(key => {
                  return {
                    key: `${key}`,
                    value: `${key}%` + (key === 100 ? '' : '以上')
                  }
                })
              )}
              onChange={(rate: any) => setFilter({
                ...filter,
                rate
              })}
            />
          </div>}
        />
        <div style={{flex: 1, overflowY: 'auto'}}>
          <Table
            loading={loading}
            // scroll={{x: 0}}
            columns={columns}
            pagination={false}
            dataSource={dataSource}
            onChange={(_, __, sortInfo: any, {action}) => {
              if (action === 'sort') {
                setSortInfo({
                  columnKey: sortInfo.field,
                  sort: sortInfo.order || 'default'
                })
              }
            }}
          />
        </div>
        {spinLoading && spin}
      </Content>
    </Layout>
  )
}

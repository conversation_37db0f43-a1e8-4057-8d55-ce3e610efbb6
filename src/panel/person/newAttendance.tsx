import React, { useState } from "react";
import ReactEcharts from 'echarts-for-react';
import { Layout, message } from 'antd';
import DescriptionsCard from '@/components/DescriptionsCard';
import opts, { axis, title, legend, color, grid } from '@/panel/echartOptions';
import { getAttendanceHomepageTrend, getAttendanceHomepageOverview } from '@/services/person';
import { useMount } from 'react-use';
import More from "@/components/more";
import { getCurrentUser } from '@/services/api';
import defaultSetting from "@/defaultSetting";
import moment from "moment";

const { Header, Content } = Layout;
export default () => {
  const currentUser = getCurrentUser();
  const [homeCount, setHomeCount] = useState<any[]>([{ label: '现场(人)', value: 0 }]);
  const [echartsOption, setEchartsOption] = useState<any>({
    title: {
      text: '近7日出勤率趋势',
      ...title,
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
      }
    },
    legend: {
      ...legend,
      data: ['全部', '建筑工人', '管理人员']
    },
    grid,
    color,
    xAxis: [
      {
        type: 'category',
        ...axis,
      }
    ],
    yAxis: [
      {
        type: 'value',
        ...axis,
      }
    ],
    series: []
  });
  useMount(() => {
    let dataObj = {
      startDate: moment().subtract(6, 'days').format('YYYY-MM-DD'),
      endDate: moment().format('YYYY-MM-DD')
    }
    getAttendanceHomepageTrend(dataObj).then((res: any) => {
      if (res.success) {
        const { result } = res
        const date: any = []
        const data: any = []
        result?.forEach?.((item: any) => {
          data.push(item.attendanceRate)
          date.push(item.recordDate)
        }) 
        setEchartsOption({
          ...echartsOption,
          xAxis: [
            {
              type: 'category',
              boundaryGap: false,
              ...axis,
              data: date
            }
          ],
          series: [
            {
              name: '出勤率',
              type: 'line',
              areaStyle: {
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [{
                    offset: 0, color: 'rgba(0,192,131,0.5)' // 0% 处的颜色
                  }, {
                    offset: 1, color: 'rgba(0,192,131,0)' // 100% 处的颜色
                  }],
                  global: false // 缺省为 false
                }
              },
              data
            }
          ]
        })
      }
    }).catch(() => {
      message.error('出错了')
    })
    getAttendanceHomepageOverview().then((res: any) => {
      if (res.success) {
        const { result } = res
        setHomeCount([
          { label: '现场(人)', value: result.presenceCnt || '--' },
          { label: '今日出勤(人)', value: result.attendanceCnt || '--' },
          { label: '在场(人)', value: result.workerCnt || '--' },
          { label: '今日出勤率(%)', value: result.attendanceRate || '--' },
        ])
      }
    }).catch(() => {
      message.error('出错了')
    })
  })
  return (
    <Layout className='layoutPanelPm'>
      <More jumpUrl={currentUser.type === 1 ? `${defaultSetting.routes.console}corPerson/attendance` : `${defaultSetting.routes.console}attendance/attendance/dailyReport#/`} />
      <Header>
        <DescriptionsCard data={homeCount} />
      </Header>
      <Content>
        <ReactEcharts
          style={{ height: '100%' }}
          opts={opts}
          option={echartsOption}
          theme="clear"
        />
      </Content>
    </Layout>
  )
}

interface optsMap {
  devicePixelRatio?: number,
  renderer?: 'canvas' | 'svg',
  width?: number | null | undefined | 'auto',
  height?: number | null | undefined | 'auto',
}
const opts: optsMap = {
  renderer: 'canvas'
};

export default opts

export const color = ['#3996FF', '#03B986', '#ffa200', '#ffbd3f', '#FC6D41', ]

export const grid = {
  top: '25%',
  left: 0,
  right: '5%',
  bottom: '3%',
  containLabel: true
}
export const axis = {
  axisTick: {
    show: false
  },
  axisLine: {
    lineStyle: {
      color: '#D8D8D8'
    },
  },
  axisLabel: {
    color: '#666666'
  },
  splitLine: {
    lineStyle: {
      type: 'dashed',
      color: '#F1F1F1'
    },
  }
}

export const title = {
  left: 0,
  padding: [5, 0, 0, 0],
  textStyle: {
    fontSize: 14,
    color: '#191919'
  }
}

export const legend = {
  right: 0,
  icon: 'rect',
  itemWidth: 10,
  itemHeight: 10,
  textStyle: {
    padding: [2, 0, 0,0]
  },
}

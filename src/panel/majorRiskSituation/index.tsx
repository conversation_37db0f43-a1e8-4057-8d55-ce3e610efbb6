import { Layout, Radio, Table } from 'antd';
import React, { useState } from 'react';
import { useRequest } from 'ahooks';
import More from '@/components/more/index';
import { getFxgkqkXmlb } from '@/services/risk';
import { getCurrentUser } from '@/services/api';
import styles from './index.less';

const { Content } = Layout;

const RadioGroup = ({ value, onChange }) => {
  return (
    <div className={styles.radio}>
      <Radio.Group
        size="small"
        optionType="button"
        options={[
          { label: '实施中', value: 1 },
          { label: '未开始', value: 2 },
          { label: '已结束', value: 3 },
        ]}
        onChange={onChange}
        value={value}
      />
    </div>
  );
};

const MajorRiskSituation = () => {
  const [value, setValue] = useState(1);
  const [dataSource, setDataSource] = useState([]);
  const currentUser = getCurrentUser();

  const { data } = useRequest(
    async () => {
      const { coId, currentDepartmentId } = currentUser;
      const res: any = await getFxgkqkXmlb({
        type: value,
        companyId: coId,
        departmentId: currentDepartmentId ? currentDepartmentId : undefined,
      });
      setDataSource(res.data);
    },
    {
      refreshDeps: [value],
    },
  );

  const columns = [
    {
      title: '所属分公司',
      dataIndex: 'department_name',
      ellipsis: true,
    },
    {
      title: '项目',
      dataIndex: 'project_name',
      ellipsis: true,
    },
    {
      title: '重大风险数（个）',
      dataIndex: 'serious_risk_num',
      ellipsis: true,
      width: 130,
    },
  ];
  function jumpPage(url: string) {
    localStorage.setItem('checkItem', '1');
    window.location.replace(url);
  }
  const handleChange = (e) => {
    const { value } = e.target;
    setValue(value);
  };

  return (
    <Layout className="layoutPanelPm" style={{ overflow: 'auto' }}>
      <Content style={{ display: 'flex' }}>
        {/* <More jumpUrl="/console/inspect/#/safety" onClick={jumpPage} /> */}
        <RadioGroup value={value} onChange={handleChange} />
        <Table
          rowClassName={(_, index) =>
            index % 2 === 0 ? 'homepanel-table-odd-row' : 'homepanel-table-even-row'
          }
          size="small"
          rowKey={(record: any) => `${record.department_name}-${record.project_name}`}
          dataSource={dataSource}
          columns={columns}
          pagination={false}
        />
      </Content>
    </Layout>
  );
};

export default MajorRiskSituation;

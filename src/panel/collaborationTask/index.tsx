import { collaGetProjectsCm } from '@/services/colla';
import { Table } from 'antd';
import React, { useEffect, useState } from 'react';

const collaTask = () => {
  const [dataSource, setDataSource] = useState<any>([]);
  const [total, setTotal] = useState<number>(0);

  const getTableData = (page: any) => {
    let data = {
      currentPage: page.current,
      pageSize: 5,
    };

    collaGetProjectsCm(data).then((res: any) => {
      setDataSource(res.data?.records);
      setTotal(res.data?.total);
    });
  };
  useEffect(() => {
    getTableData({ current: 1 });
  }, []);

  const columns: any = [
    {
      title: '项目名称',
      dataIndex: 'projectName',
      ellipsis: true,
      key: 'projectName',
    },
    {
      title: '进行中任务',
      dataIndex: 'doingCount',
      key: 'doingCount',
      width: 100,
      render: (data: number) => {
        return <span style={{ color: '#2196F3' }}>{data}</span>;
      },
    },
    {
      title: '已完成任务',
      dataIndex: 'doneCount',
      key: 'doneCount',
      width: 100,
      render: (data: number) => {
        return <span style={{ color: '#4CAF50' }}>{data}</span>;
      },
    },
    {
      title: '已延期任务',
      dataIndex: 'delayCount',
      key: 'delayCount',
      width: 100,
      render: (data: number) => {
        return <span style={{ color: '#F44336' }}>{data}</span>;
      },
    },
    {
      title: '任务总数',
      dataIndex: 'allCount',
      key: 'allCount',
      width: 100,
    },
  ];
  return (
    <div>
      <Table
        size="small"
        dataSource={dataSource}
        columns={columns}
        pagination={{
          total: total,
          pageSize: 5,
          hideOnSinglePage: true,
        }}
        onChange={getTableData}
      />
    </div>
  );
};
export default collaTask;

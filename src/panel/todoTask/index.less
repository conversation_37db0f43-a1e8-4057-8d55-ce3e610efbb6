.panelList {
  position: relative;
  box-sizing: content-box;
  width: 100%;
  padding: 0 5px 5px 5px;
  .panelHeader {
    height: 30px;
    margin-top: -7px;
    font-weight: bold;
    font-size: 16px;
  }
  .panelContent {
    position: relative;
    display: flex;
    flex-direction: column;
    height: 364px;
  }
}

.todoContainer {
  display: flex;
  flex: 1;
  width: 100%;
  height: 100%;
  margin-top: 15px;
  .todoListContainer {
    flex: 1;
    width: 100%;
    height: 268px;
    .noticeItem {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      padding: 9px 5px 9px 0px;
      border-bottom: 1px solid #eee;
      .textContainer {
        flex: 0.9;
        .noticeItemTitle {
          display: -webkit-box;
          width: 100%;
          padding-bottom: 2px;
          overflow: hidden;
          color: #303133;
          font-weight: 700;
          font-size: 16px;
          font-family: PingFangSC-Medium;
          white-space: pre-line;
          text-overflow: ellipsis;
          word-break: break-all;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
        }
        .noticeItemText {
          width: 100%;
          padding-top: 2px;
          overflow: hidden;
          color: #909399;
          font-weight: 400;
          font-size: 14px;
        }
      }
      .actionBtn {
        color: #004EA0;
        cursor: pointer;
      }
    }
  }
}

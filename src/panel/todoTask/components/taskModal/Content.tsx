import { windowOpen } from '@/utils/windowOpen';
import { CopyFilled, ExclamationCircleFilled, RightOutlined } from '@ant-design/icons';
import { Col, Empty, message, Row } from 'antd';
import React, { useMemo, useState } from 'react';
import { useMount } from 'react-use';
import { MemberTitle, ProjectTitle } from '@pms/react-retrieve';
import './index.less';

interface IProps {
  type?: number;
  currentObj?: any;
  detail?: any;
  onCancel?: () => void;
}

const { PmsComponents } = window.__PMS_CONSOLE__;

export default (props: IProps) => {
  const { origin } = window.location;
  const { detail, currentObj, type, onCancel } = props;
  const [src, setSrc] = useState('');
  const colSpan = 8;

  const hiddenDetail = useMemo(() => {
    return detail.hiddenDetail;
  }, [detail]);

  const goToPage = () => {
    console.log('------去子应用页面', detail);
    // 菜单抛出的方法，但是得在运营中心外面使用
    // menu.onClickPlug(detail.plugNo)
    // 	.then(() => {
    // 		onCancel && onCancel()
    // 	})
    // 	.catch((e) => {
    // 		// console.log(e)
    // 		message.error(e.errorMsg);
    // 	})
    if (detail.plugObj) {
      if (detail.plugObj.frontUrl) {
        windowOpen(`/console${detail.plugObj.webUrl}`);
      } else {
        windowOpen(detail.plugObj.webUrl);
      }
    } else {
      message.error('找不到该应用，无法进行跳转');
    }
  };

  // 复制方法
  const copy = (text) => {
    let input = document.createElement('input');
    input.setAttribute('id', 'input_for_copyText');
    input.value = text;
    document.getElementsByTagName('body')[0].appendChild(input);
    document.getElementById('input_for_copyText').select();
    document.execCommand('copy');
    document.getElementById('input_for_copyText').remove();
    message.success('复制成功');
  };
  useMount(() => {
    if (detail && detail.viewUrl) {
      const origin = window.location.origin;
      // const origin = 'https://gzdj-test.pinming.org' // 用于本地调试测试环境页面
      const paramsString = 'header=false&menu=false';
      if (detail.viewUrl.includes('?')) {
        setSrc(origin + detail.viewUrl + '&' + paramsString);
      } else {
        setSrc(origin + detail.viewUrl + '?' + paramsString);
      }
    }
  });
  return (
    <div className="task-content">
      {!hiddenDetail && (
        <div className="content-top">
          {/* title标题和业务来源 */}
          <div className="content-top-title">
            <div className="content-top-title-left">{detail.subject}</div>
            <div>
              业务来源：
              <span
                className="cursor-font"
                onClick={() => {
                  goToPage();
                }}
              >
                {detail[type === 1 ? 'bizSourceName' : 'defName']}
                <RightOutlined />
              </span>
            </div>
          </div>
          {/* 描述内容 */}
          <div style={{ marginBottom: 12 }}>{detail.description}</div>
          <Row>
            <Col className="form-item" span={colSpan}>
              <div className="form-item-label">发起人：</div>
              <div className="form-item-content">
                <MemberTitle suffixIcon={true} id={detail.memberId} />
              </div>
            </Col>
            <Col className="form-item" span={colSpan}>
              <div className="form-item-label">发起时间：</div>
              <div className="form-item-content">{detail.createTime}</div>
            </Col>
            <Col className="form-item" span={colSpan}>
              <div className="form-item-label">
                {/* 要求完成时间或者是完成时间 */}
                {currentObj.completeDateString}：
              </div>
              <div className="form-item-content">{detail[currentObj.completeDate] || '--'}</div>
            </Col>
            <Col className="form-item" span={colSpan}>
              <div className="form-item-label">业务表单：</div>
              <div className="form-item-content">
                {detail.formNum ? (
                  <span>
                    <span>{detail.formNum}</span>
                    <CopyFilled
                      onClick={() => {
                        copy(detail.formNum);
                      }}
                      className="cursor-font"
                      style={{ marginLeft: 5 }}
                    />
                  </span>
                ) : (
                  '--'
                )}
              </div>
            </Col>
            <Col className="form-item" span={colSpan}>
              <div className="form-item-label">所属组织：</div>
              <div className="form-item-content">
                {detail.projectId ? (
                  <ProjectTitle suffixIcon={true} id={detail.projectId} />
                ) : (
                  detail.organize
                )}
              </div>
            </Col>
          </Row>
        </div>
      )}
      {src ? (
        <iframe
          style={{
            border: 'none',
            padding: 0,
            margin: 0,
            width: '100%',
            height: hiddenDetail ? 'calc(100vh - 170px)' : 'calc(100vh - 290px)',
            marginTop: !hiddenDetail ? 20 : 0,
          }}
          src={src}
        />
      ) : (
        <div
          style={{
            width: '100%',
            height: 'calc(100vh - 290px)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <Empty
            image={<ExclamationCircleFilled style={{ color: '#FF4D4F', fontSize: 70 }} />}
            description={
              <div>
                抱歉，当前任务不支持Web端办理或查看
                <br />
                请登录移动端，进入【待办任务】中进行操作。
              </div>
            }
          />
        </div>
      )}
    </div>
  );
};

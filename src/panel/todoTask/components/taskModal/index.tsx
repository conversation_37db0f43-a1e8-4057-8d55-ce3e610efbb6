import { Modal } from 'antd';
import React from 'react';
import { useEffectOnce } from 'react-use';
import Content from './Content';
import './index.less';

interface IProps {
  type: number;
  currentObj: any;
  visible: boolean;
  detail: any;
  onCancel: () => void;
}

export default (props: IProps) => {
  const { notify } = window.__PMS_CONSOLE__
  const { visible, onCancel, detail, currentObj, type } = props;

  useEffectOnce(() => {
    // TODO 发起任务是由流程引擎开发，那边成功之后，会发起回调，通知这边成功，关闭窗口，刷新列表
    window.addEventListener(
      'message',
      (MessageEvent) => {
        if (MessageEvent?.data?.includes?.('bpmServer_success')) {
          // 通知主应用的消息图标更新未读消息数量
          notify?.setTodoMsg();
          onCancel && onCancel();
        }
      },
      false,
    );
  });

  return (
    <Modal
      className="task-modal"
      title="任务详情"
      visible={visible}
      onCancel={onCancel}
      bodyStyle={{
        padding: '20px',
        height: 'calc(100vh - 100px)',
        overflow: 'auto',
      }}
      forceRender={true}
      centered={true}
      footer={false}
    >
      {visible && (
        <Content detail={detail} type={type} currentObj={currentObj} onCancel={onCancel} />
      )}
    </Modal>
  );
};

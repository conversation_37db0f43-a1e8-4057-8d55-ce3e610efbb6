// 待办任务
import EmptyCenter from '@/components/EmptyCenter';
import More from '@/components/more';
import { fetchMyTodoList } from '@/services/todoTask';
import { Card, Layout } from 'antd';
import React, { useEffect, useState } from 'react';
import TaskModal from './components/taskModal/index';
import Styles from './index.less';

const { Content } = Layout;

export default () => {
  const [todoList, setTodoList] = useState([]);
  const [visible, setVisible] = useState(false);
  const [detail, setDetail] = useState<any>({});

  // 处理待办按钮
  const dealTodoTask = (item: any) => {
    setDetail(item);
    setVisible(true);
  };

  // 获取待办任务
  const getMyTodoList = () => {
    const params = {
      current: 1,
      size: 4,
    };
    fetchMyTodoList(params).then((res: any) => {
      setTodoList(res.data?.records ?? []);
    });
  };

  useEffect(() => {
    getMyTodoList();
  }, []);

  return (
    <>
      <Layout className="layoutPanelPm">
        <Content style={{ display: 'flex' }}>
          <More content="更多" jumpUrl="/console/taskCenter/#/list" />
          <div className={Styles.todoContainer}>
            {todoList.length ? (
              <>
                <div className={Styles.todoListContainer}>
                  {todoList?.map((item: any, index: number) => {
                    return (
                      <div
                        className={Styles.noticeItem}
                        key={index}
                        style={{ borderBottomWidth: index === 3 ? 0 : 1 }}
                      >
                        <div className={Styles.textContainer}>
                          <div className={Styles.noticeItemTitle}>{item.name}</div>
                          <div className={Styles.noticeItemText}>
                            <span>{`来源：${item.busiTitle}`}</span>
                            <span style={{ paddingLeft: 22 }}>{`要求完成时间：${
                              item.completeDate ?? '-'
                            } (${item.taskExtraStatusName})`}</span>
                          </div>
                        </div>
                        <div className={Styles.actionBtn} onClick={() => dealTodoTask(item)}>
                          处理
                        </div>
                      </div>
                    );
                  })}
                </div>
              </>
            ) : (
              <EmptyCenter description="暂无数据" />
            )}
          </div>
        </Content>
      </Layout>
      <TaskModal
        type={1}
        currentObj={{
          situation: '我办理的-我的代办',
          completeDateString: '要求完成时间',
          completeDate: 'completeDate',
        }}
        visible={visible}
        detail={detail}
        onCancel={() => {
          setVisible(false);
          getMyTodoList();
        }}
      />
    </>
  );
};

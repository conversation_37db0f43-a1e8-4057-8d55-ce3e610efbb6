import More from '@/components/more'
import { FieldTimeOutlined, ReadOutlined } from '@ant-design/icons'
import { Layout } from 'antd'
import React, { useEffect, useState } from 'react'
import Styles from './index.less'
import { getApproveInfo } from '../../services/myApprove'
import Empty from 'antd/es/empty'
const { Content } = Layout
export default () => {

  const [toPerform, setToPerformt] = useState<any>([]) // 存放关于“我”待执行的流程数据  用于展示
  const [delayCount, setDelayCount] = useState<any>()  // 存放延迟的总数
  const [allList, setAllList] = useState<any>()  // 计算总数
  const getApprove = () => {
    getApproveInfo().then(res=>{
      console.log(res,'resd');
      if (res.success) {
            let dataSource: any[] = []
            dataSource = res.data
            // 针对返回的时间进行升序
            dataSource.sort(function (a: any, b: any) {
              return a.time < b.time ? -1 : 1
            });
            setAllList(dataSource)
            setToPerformt(dataSource ? dataSource.slice(0, 5) : "")
            let count = 0;
            dataSource.forEach((item: any) => {
              if (item.delayFlag == true) {
                count++
              }
            })
            setDelayCount(count)
          }
    })
  }
  useEffect(() => {
    getApprove()
  }, [])

  return (
    <div className={Styles.myApproval}>
      <Content className={Styles.mainCon}>
        <div className={Styles.more} style={{ width: '85px', height: '30px', lineHeight: '30px', position: 'absolute', right: '0', top: '0', cursor: 'pointer' }} >
          <More content='查看更多' jumpUrl='/console/bim/flowManagement' />
        </div>
        <div className={Styles.icons} style={{ height: '30px', display: 'flex' }}>
          <div className={Styles.firsticon}><ReadOutlined style={{ fontSize: '20px' }} />&nbsp;<span>{allList?.length}</span></div>
          &nbsp;&nbsp;
          <div className={Styles.seconedIcon}><FieldTimeOutlined style={{ fontSize: '20px', color: 'red' }} />&nbsp;<span>{delayCount ? delayCount : 0}</span></div>
        </div>
        <ul className={Styles.ulCont}>
          {
            toPerform.length > 0 ? toPerform.map((item: any) => {
              return <li className={item.delayFlag == true ? Styles.liLists : Styles.liList} key={item.wfOrderId}>
                <div className={Styles.lists}>
                  <div className={Styles.listsName}>{item.processName}</div>
                  <div className={Styles.listsTime}>{(item.finishTime).substring(0,10)}</div>
                </div>
              </li>
            }) : <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
          }
        </ul>
      </Content>
    </div>
  )
}

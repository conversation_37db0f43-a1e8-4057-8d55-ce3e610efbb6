import React, { useEffect, useState } from 'react'
import { Column } from '@ant-design/charts';
import * as echarts from 'echarts';
import ReacrEcharts from 'echarts-for-react'
type EchartOption = echarts.EChartsOption;
import Styles from './index.less'
import { getProblemType } from '@/services/problemsStatist';

export default function index() {

  const [problemList, setProblemList] = useState<any>([])

  const getProblemList = () => {
    getProblemType().then((res: any) => {
      // console.log(res.data.data, '问题统计');
      if (res?.success) {
        // let dataList = res.data.data
        let dataSource: any = []
        dataSource = res.data.data ? res.data.data : res.data
        dataSource.sort(function (a: any, b: any) {
          return b.count - a.count
        })
        setProblemList(dataSource)
        // setProblemList(res.data.data ? res.data.data : res.data)
      }
    })
  }
  useEffect(() => {
    getProblemList()
  }, [])

  const proStatistic = () => {
    const option: EchartOption = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      legend: {},
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: [
        {
          type: 'category',
          // data: ['图纸问题', '模型质量', '专业冲突', '净高不足', '需求变更', '加工质量', '质量管理', '工期管理', '高度管理'],
          data: problemList.map((item: any) => {
            return item.typeName
          }),
          axisTick: {
            show: false
          },
          axisLabel: {
            textStyle: { //改变xy轴上文字的颜色
              color: "rgb(90,90,90)",
              fontSize: 10
            },
          },
          axisLine: {
            type: 'solid',
            lineStyle: {
              color: 'rgb(213, 213, 213)',//坐标线的颜色
            }
          },
        }
      ],
      yAxis: [
        {
          type: 'value',
          axisTick: {
            show: false
          },
          axisLine: {
            show: false  //不显示坐标轴轴线
          },
          axisLabel: {
            textStyle: {//改变xy轴上文字的颜色
              color: "rgb(90,90,90)"
            }
          },
        }
      ],
      series: [

        {
          name: '未处理',
          type: 'bar',
          stack: 'Ad',
          emphasis: {
            focus: 'series'
          },
          // data: [10, 20, 11, 6, 6, 20],
          data: problemList.map((item: any) => {
            return item.todoCount ? item.todoCount : 0
          }),
          color: '#ff9300',
          //barGap:'10%',
          //barCategoryGap:'90%'
        },
        {
          name: '已处理',
          type: 'bar',
          stack: 'Ad',
          emphasis: {
            focus: 'series'
          },
          data: problemList.map((item: any) => {
            // return item.count
            return item.doneCount ? item.doneCount : 0
          }),
          color: '#46c292',
          barWidth: 25,
          //barGap:'80%',
          barCategoryGap: '50%'
        },
      ],
      dataZoom: [
        {
          type: 'slider',
          show: problemList.length > 6 ? true : false,
          handleSize: 0,//滑动条的 左右2个滑动条的大小
          startValue: 0, // 初始显示值
          endValue: 5, // 结束显示值
          height: 5,//组件高度
          left: '5%', //左边的距离
          right: '4%',//右边的距离
          bottom: 0,//底边的距离
          borderColor: "#c1c1c1",
          fillerColor: '#c1c1c1',
          borderRadius: 20,
          backgroundColor: '#f1f1f1',//两边未选中的滑动条区域的颜色
          showDataShadow: false,//是否显示数据阴影 默认auto
          showDetail: false,//即拖拽时候是否显示详细数值信息 默认true
          realtime: true, //是否实时更新
          filterMode: 'filter',
        },
      ]
    }
    return option
  }
  return (
    <div className={Styles.problemsStatistical}>
      <div className='echartContent'>
        <ReacrEcharts option={proStatistic()} />
      </div>
    </div>
  )
}



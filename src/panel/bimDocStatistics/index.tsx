import { companyDocTypeUploadStatsApi, projectDocTypeUploadStatsApi } from '@/services/bim';
import React, { useEffect, useState } from 'react';

import { getCurrentUser } from '@/services/api';
import ReacrEcharts from 'echarts-for-react';

const DoughnutColor = [
  '#5B8FF9',
  '#5AD8A6',
  '#5D7092',
  '#F6BD16',
  '#E86452',
  '#6DC8EC',
  '#945FB9',
  '#FF9845',
  '#1E9493',
  '#FF99C3',
];

let observer: MutationObserver | undefined;
let oldWidth = 0;

const BimDocStatistics = () => {
  const currentUser = getCurrentUser();

  const [doughnutData, setDoughnutData] = useState<any>({ text: '', subtext: '', data: [] });
  const [hide, setHide] = useState<boolean>(false);

  const getChartInfo = () => {
    if (currentUser.type == 2) {
      projectDocTypeUploadStatsApi().then((res) => {
        if (res.success) {
          let sum = 0,
            subtext;
          res.data.map((item: any) => {
            sum += item.value;
          });
          if (sum > 10000) {
            subtext = `${sum / 10000}{small|万}`;
          } else {
            subtext = `${sum}{small|个}`;
          }
          // initDoughnutChart({ text: '总文件数', subtext, data: res.data });
          setDoughnutData({ text: '总文件数', subtext: `${sum}`, data: res.data });
        }
      });
    } else {
      companyDocTypeUploadStatsApi().then((res) => {
        if (res.success) {
          let sum = 0,
            subtext;
          res.data.map((item: any) => {
            sum += item.value;
          });
          if (sum > 10000) {
            subtext = `${sum / 10000}{small|万}`;
          } else {
            subtext = `${sum}{small|个}`;
          }
          // initDoughnutChart({ text: '总文件数', subtext, data: res.data });
          setDoughnutData({ text: '总文件数', subtext: `${sum}`, data: res.data });
        }
      });
    }
  };

  /**
   * 生成环形图
   * @params hide 是否隐藏图例
   */
  const initDoughnutChart = (hide: boolean) => {
    // let myChart = echarts.init(document.getElementById('docStatisticChart')!);

    let option: any = {
      tooltip: {
        trigger: 'item',
        formatter(params: any) {
          let value;
          if (params.value > 10000) {
            value = `${params.value / 10000}万`;
          } else {
            value = `${params.value}个`;
          }
          return `${params.marker} ${params.name}：${params.percent}%&emsp;&emsp;${value}`;
        },
      },
      title: {
        show: true,
        text: doughnutData.text,
        subtext: doughnutData.subtext,
        top: '41.5%',
        left: 'center',
        textStyle: {
          fontWeight: 300,
          fontSize: 12,
        },
        subtextStyle: {
          fontWeight: 600,
          fontSize: 24,
          color: '#454545',
          rich: {
            small: {
              // color: 'red',
              fontSize: 14,
              height: 20,
              verticalAlign: 'bottom',
            },
          },
        },
      },
      // legend: {
      //   orient: 'vertical',
      //   top: 'center',
      //   right: '5%',
      //   icon: 'circle',
      // },
      series: [
        {
          type: 'pie',
          radius: ['55%', '85%'],
          avoidLabelOverlap: false,
          label: {
            show: true,
            position: 'inside',
            // formatter: '{d}%',
            formatter(params: any) {
              let value;
              if (params.percent > 5) {
                value = `${params.percent}%`;
              } else {
                value = ``;
              }
              return value;
            },
          },
          emphasis: {
            // 鼠标悬浮
            label: {
              show: true,
              fontSize: '20',
              fontWeight: 'bold',
            },
          },
          labelLine: {
            show: false,
          },
          color: DoughnutColor,
          data: doughnutData.data,
        },
      ],
    };
    let width = document.getElementById('docStatisticChart')?.clientWidth ?? 0;
    if (!hide && width >= 480) {
      option['legend'] = {
        orient: 'vertical',
        top: 'center',
        right: '3%',
        icon: 'circle',
      };
    }

    // option && myChart.setOption(option);

    return option;
  };

  //浏览器窗口大小改变
  const resizeChange = () => {
    let width = document.getElementById('docStatisticChart')?.clientWidth ?? 0;

    if (width === oldWidth) return;
    oldWidth = width;
    if (width < 480) {
      // initDoughnutChart(true);
      setHide(true);
    } else {
      setHide(false);
      // initDoughnutChart(false);
    }
  };

  useEffect(() => {
    getChartInfo();
  }, []);

  useEffect(() => {
    // 监听
    let MutationObserver = window.MutationObserver;
    let element: any = document.getElementById('docStatisticChart');
    observer = new MutationObserver(resizeChange);

    observer.observe(element as Node, {
      attributes: true,
      childList: true,
      subtree: true,
    });

    // 销毁
    return () => {
      observer?.disconnect();
    };
  }, []);

  return (
    <div
      id="docStatisticChart"
      style={{
        width: '100%',
        height: '100%',
      }}
    >
      {/**@ts-ignore */}
      <ReacrEcharts option={initDoughnutChart(hide)} notMerge={true} />
    </div>
  );
};

export default BimDocStatistics;

import Class from './index.less';
import React, {useState} from "react";
import {useMount} from 'react-use';
import { Layout, message } from 'antd';
import ReactEcharts from 'echarts-for-react';
import DescriptionsCard from '@/components/DescriptionsCard';
import PieCharts from '@/components/charts/percentPie/index';
import opts, {axis, title, color, grid} from '@/panel/echartOptions';
import {fetchInspectBoard} from '@/services/qualitySecurity';
import More from '@/components/more/index';
const {Content} = Layout

export default () => {
  const [data , setData] = useState({} as any)
  const [xAxisData, setXAxisData] = useState([])
  const [yAxisData, setYAxisData] = useState([])

  useMount(() => {
    fetchInspectBoard(1).then((res: any) => {
      if (res.errorMsg) {
        message.error(res.errorMsg)
      } else {
        setData(res.board)
        let xAxisD: any = []
        let yAxisD: any = []
        res.board.sevenList.forEach((item: any) => {
          xAxisD.push(item.date)
          yAxisD.push(item.rectifyRate)
        })
        setXAxisData(xAxisD)
        setYAxisData(yAxisD)
      }
    })
  })

  function jumpPage(url: string) {
    localStorage.setItem('checkItem', '1')
    window.location.replace(url)
  }

  const echartsOption = {
    title: {
      text: '近7日安全整改率',
      ...title,
      left: 'center',
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
      }
    },
    grid,
    color,
    xAxis: [
      {
        type: 'category',
        ...axis,
        data: xAxisData
      }
    ],
    yAxis: [
      {
        type: 'value',
        ...axis,
      }
    ],
    series: [
      {
        type: 'bar',
        barWidth: 15,
        data: yAxisData
      },
    ]
  }
  return (
    <Layout className='layoutPanelPm'>
      <Content style={{display: 'flex'}}>
        <More jumpUrl="/console/inspect/#/safety" onClick={jumpPage} />
        <section style={{ width: '40%', height: 291 }}>
          <PieCharts opts={{subTitle: '整改率', title: `${data.rectifyRate || 0}%`}} data={data.rectifyRate || 0} />
        </section>
        <section style={{ width: '60%', height: 291, display: 'flex', flexDirection: 'column' }}>
          <DescriptionsCard
            className={Class.securityDescriptionsCard}
            data={[
              {label: '整改中(项)', value: data.noRectifyNum ?? '--'},
              {label: '待复检(项)', value: data.noCheckedNum ?? '--'},
              {label: '已完成(项)', value: data.finishNum ?? '--'},
            ]}/>
          <ReactEcharts
            style={{height: '100%'}}
            opts={opts}
            option={echartsOption}
            theme="clear"/>
        </section>
      </Content>
    </Layout>
  )
}

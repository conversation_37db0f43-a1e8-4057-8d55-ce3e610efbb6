import Class from './index.less';
import React, {useState} from "react";
import {useMount} from 'react-use';
import { Layout, message } from 'antd';
import ReactEcharts from 'echarts-for-react';
import DescriptionsCard from '@/components/DescriptionsCard';
import PieCharts from '@/components/charts/percentPie/index';
import opts, {axis, title, color, grid} from '@/panel/echartOptions';
import {fetchInspectBoard} from '@/services/qualitySecurity';
import More from '@/components/more/index';
const {Content} = Layout

export default () => {
  const [data, setData] = useState({} as any)
  const [xAxisData, setXAxisData] = useState([])
  const [yAxisData, setYAxisData] = useState([])

  useMount(() => {
    fetchInspectBoard(2).then((res: any) => {
      if (res.errorMsg) {
        message.error(res.errorMsg)
      } else {
        setData(res.board)
        let xAxisD: any = []
        let yAxisD: any = []
        res.board.sevenList.forEach((item: any) => {
          xAxisD.push(item.date)
          yAxisD.push(item.rectifyRate)
        })
        setXAxisData(xAxisD)
        setYAxisData(yAxisD)
      }
    })
  })

  const echartsOption = {
    title: {
      text: '近7日质量整改率',
      ...title,
      left: 'center',
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
      }
    },
    grid,
    color,
    xAxis: [
      {
        type: 'category',
        ...axis,
        data: xAxisData
      }
    ],
    yAxis: [
      {
        type: 'value',
        ...axis,
      }
    ],
    series: [
      {
        type: 'bar',
        barWidth: 15,
        data: yAxisData
      },
    ]
  }

  function jumpPage(url: string) {
    localStorage.setItem('checkItem', '2')
    window.location.replace(url)
  }

  return (
    <Layout className='layoutPanelPm'>
      <Content style={{display: 'flex'}}>
        <More jumpUrl="/console/inspect/#/quality" onClick={jumpPage} />
        <section style={{ width: '40%', height: 291 }}>
          <PieCharts opts={{subTitle: '整改率', title: `${data.rectifyRate || 0}%`}} data={data.rectifyRate || 0} />
        </section>
        <section style={{ width: '60%', height: 291, display: 'flex', flexDirection: 'column' }}>
          <DescriptionsCard
            className={Class.qualityDescriptionsCard}
            data={[
              {label: '整改中(项)', value: data.noRectifyNum ?? '--'},
              {label: '待复检(项)', value: data.noCheckedNum ?? '--'},
              {label: '已完成(项)', value: data.finishNum ?? '--'},
            ]}/>
          <ReactEcharts
            style={{height: '100%'}}
            opts={opts}
            option={echartsOption}
            theme="clear"/>
        </section>
      </Content>
    </Layout>
  )
}

import Class from './warning.less';
import React, {useState} from "react";
import {useMount} from "react-use";
import { Layout, List, } from 'antd';
import DescriptionsCard from '@/components/DescriptionsCard';
import PieCharts from '@/components/charts/percentPie/index';
// @ts-ignore
import Scrollbars from 'react-custom-scrollbars';
import {fetchProjectNodeOneRate, fetchProjectNodeTwoRate, fetchWarnTaskList, fetchTaskWarnStatistic} from '@/services/progress';
import More from '@/components/more/index';
const {Content} = Layout

export default () => {
  const [oneRate , setOneRate] = useState(0)
  const [twoRate , setTwoRate] = useState(0)
  const [taskStatic, setTaskStatic] = useState({} as any)
  const [list, setList] = useState([])

  useMount(() => {
    fetchProjectNodeOneRate().then((res:any) => {
      setOneRate(res.data.conRate)
    })
    fetchProjectNodeTwoRate().then((res:any) => {
      setTwoRate(res.data.conRate)
    })
    fetchWarnTaskList().then((res:any) => {
      setList(res.data)
    })
    fetchTaskWarnStatistic().then((res: any) => {
      setTaskStatic(res.data)
    })
  })

  const renderThumb = (payload: any) => {   // 设置滚动条的样式
    const thumbStyle = {
      backgroundColor: '#000',
      borderRadius: '6px',
      opacity: 0,
      ...payload
    }
    return <div style={{...thumbStyle }}/>
  }

  return (
    <Layout className='layoutPanelPm'>
      <Content style={{display: 'flex'}}>
        <More jumpUrl="/console/schedule-plan/#/schedule-plan/schedulePlan" content="查看更多" />
        <section style={{ width: '25%', height: '100%' }}>
          <PieCharts opts={{subTitle: '一级节点受控率', title: `${oneRate}%`}} data={oneRate} />
        </section>
        <section style={{ width: '25%', height: '100%' }}>
         <PieCharts opts={{subTitle: '二级节点受控率', title: `${twoRate}%`}} data={twoRate} />
        </section>
        <section style={{ width: '50%', height: '100%', display: 'flex', flexDirection: 'column' }}>
          <DescriptionsCard
            className={Class.warningDescriptionsCard}
            data={[
              {label: '一般延误(项)', value: taskStatic?.generalTaskCount ?? '--'},
              {label: '较大延误(项)', value: taskStatic?.largerTaskCount ?? '--'},
              {label: '重大延误(项)', value: taskStatic?.greatTaskCount ?? '--'},
            ]}/>
            <Scrollbars renderThumbVertical={() => renderThumb({ right: '4px',  width: '8px'})}>
              <List
                className={Class.listWrapper}
                itemLayout="vertical"
                dataSource={list}
                renderItem={(item: any) => (
                  <List.Item key={item.taskId} className={Class.item}>
                    <div className={Class.name}>{item.taskName}</div>
                    <div className={`${Class.status} ${Class[`status--${item.warnType}`]}`}>{item.scheduleConditStr}</div>
                  </List.Item>
                )}
              />
            </Scrollbars>
        </section>
      </Content>
    </Layout>
  )
}

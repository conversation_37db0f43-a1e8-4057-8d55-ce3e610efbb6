.warningDescriptionsCard {
  height: auto !important;
  >div{
    &:nth-child(1) {
      color: #00C586 !important;
    }
    &:nth-child(2) {
      color: #FFA200 !important;
    }
    &:nth-child(3) {
      color: #FC6D41 !important;
    }
  }
}
.listWrapper {
  border: 1px solid #ECECEC;
  .item {
    display: flex;
    justify-content: space-between;
    padding: 11px 15px;
    &:nth-child(odd){
      background: #f5f5f5;
    }
    .name {
      font-size: 14px;
      color: #191919;
      font-weight: bolder;
      line-height: 21px;
    }
    .status {
      width: 85px;
      height: 24px;
      line-height: 24px;
      text-align: center;
      border-radius: 12px;
      color: #fff;
      &--1 {
        background: #00C586;
      }
      &--2 {
        background: #FFA200;
      }
      &--3 {
        background: #FC6D41;
      }
    }
  }
}

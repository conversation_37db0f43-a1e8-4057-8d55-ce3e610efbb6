import React, { useState } from 'react';
import { Layout } from 'antd';
import DoublePrecentPie from '@/components/charts/doublePercentPie/index';
import { PieProps } from '@/components/charts/percentPie/type';
import { useEffectOnce } from 'react-use';
import style from './index.less';
import { COLUMNSLIST } from './_data';
import { getSchedulePlanDeadline } from '@/services/progress.ts';
import More from '@/components/more/index';

// 项目进度
export default () => {
  const { Content } = Layout;
  const [opts, setOpts] = useState({} as PieProps['opts']);
  const [pieData, setPieData] = useState([23, 56]);
  const [progressObj, setProgressObj] = useState({} as any);

  const getData = async () => {
    const res = await getSchedulePlanDeadline();
    if (res && res.data) {
      const { data } = res;
      setPieData([data.actualSchedule, data.planSchedule]);
      setOpts({ subTitle: '实际进度', title: `${data.actualSchedule || 0}%` });
      setProgressObj(data);
    }
  };

  useEffectOnce(() => {
    getData();
  });

  return (
    <Layout className='layoutPanelPm'>
      <Content>
        <More jumpUrl="/console/schedule-plan/#/schedule-plan/schedulePlan" />
        <section className={style.progress_pj}>
          <article style={{ width: '50%', height: '100%' }}>
            <DoublePrecentPie opts={opts} data={pieData} />
          </article>
          <article className={style.progress_con}>
            <div className={style.top}>
              <p>
                <b style={{ color: '#00C58E' }}>{progressObj.planSchedule || 0}%</b>
                <span>计划进度</span>
              </p>
              <p>
                <b style={{ color: `${progressObj.schedule < 0 ? '#fc744b' : '#00c58e'}` }}>
                  {Math.abs(progressObj.schedule || 0)}
                  <i> 天</i>
                </b>
                <span className={style.tag_box}>
                  总进度
                  <span
                    className={style.tag}
                    style={{ background: `${progressObj.schedule < 0 ? '#fc744b' : '#00c58e'}` }}
                  >
                    {progressObj.schedule < 0 ? '滞后' : '正常'}
                  </span>
                </span>
              </p>
            </div>
            <div className={style.bottom}>
              {COLUMNSLIST.map(item => (
                <p key={item.key}>
                  <span>{item.label}</span>
                  <span>{progressObj[item.value]}</span>
                </p>
              ))}
            </div>
          </article>
        </section>
      </Content>
    </Layout>
  );
};

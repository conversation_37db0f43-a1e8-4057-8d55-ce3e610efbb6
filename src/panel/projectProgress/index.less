.progress_pj {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  font-size: 14px;
  width: 100%;
  height: 100%;

  .progress_con {
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 50%;

    .top {
      display: flex;
      align-items: center;
      justify-content: space-around;
      width: 90%;
      margin: 0 auto;
      border: 1px solid #f1f1f1;
      padding: 5px;

      p {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        color: #666;

        b {
          font-size: 36px;

          i {
            font-style: normal;
            font-size: 14px;
            color: #666;
          }
        }
      }

      .tag_box {
        display: flex;
        align-items: center;

        .tag {
          padding: 0px 6px;
          color: #fff;
          font-size: 12px;
          border-radius: 10px;
          margin-left: 5px;
        }
      }
    }

    .bottom {
      padding: 10px 0;
      width: 90%;
      margin: 15px auto 0 auto;
      p {
        display: flex;
        align-items: center;
        justify-content: space-between;
        box-sizing: border-box;
        margin-bottom: 10px;
        font-size: 16px;
        color: #191919;
      }
    }
  }
}

// 未来六个月风险趋势分析
import EmptyCenter from '@/components/EmptyCenter';
import opts, { axis, color, grid } from '@/panel/echartOptions';
import { useRequest } from 'ahooks';
import { Layout } from 'antd';
import ReactEcharts from 'echarts-for-react';
import React, { useMemo } from 'react';

import { getFutureList } from '../api';
const FutureRiskTrend = () => {
  const { data } = useRequest(async () => {
    const res = await getFutureList();
    return res?.data || [];
  });

  return useMemo(() => {
    if (!data?.length) return <EmptyCenter description="暂无数据" />;

    const echartsOption = {
      legend: {
        data: ['重大风险', '较大风险', '一般风险', '低风险'],
        icon: 'rect',
        itemWidth: 10, // 矩形宽度
        itemHeight: 10, // 矩形高度
        top:10
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
        },
      },
      grid,
      color,
      xAxis: [
        {
          type: 'category',
          ...axis,
          data: data?.map((item: any) => item.stat_month),
        },
      ],
      yAxis: [
        {
          type: 'value',
          ...axis,
          name: '数量/个',
          nameTextStyle: {
            color: '#666666',
          },
        },
      ],
      series: [
        {
          type: 'line',
          name: '重大风险',
          smooth: true,
          data:data.map((item:any)=>item.serious_num),
        },
        {
          type: 'line',
          name: '较大风险',
          smooth: true,
          data:data.map((item:any)=>item.larger_num),
        },
        {
          type: 'line',
          name: '一般风险',
          smooth: true,
          data:data.map((item:any)=>item.general_num),
        },
        {
          type: 'line',
          name: '低风险',
          smooth: true,
          data:data.map((item:any)=>item.low_num),
        },
      ],
      color: ['#FF4D4F', '#FF9400', '#FFCD00', '#2478FF'],  // 自定义线条颜色
    };

    return (
      <Layout className="layoutPanelPm">
        <Layout.Content>
          <ReactEcharts
            style={{ height: '98%' }}
            opts={opts}
            option={echartsOption}
            theme="clear"
          />
        </Layout.Content>
      </Layout>
    );
  }, [data]);
};

export default FutureRiskTrend;

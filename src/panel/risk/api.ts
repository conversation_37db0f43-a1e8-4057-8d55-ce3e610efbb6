const { request } = window.PMHOME;
export const getCurrentUser = () => {
  const { getCurrentUser } = window.PMHOME;
  return getCurrentUser();
};

export const formatDeptParam = () => {
  const { coId, pjId, currentDepartmentId, type } = getCurrentUser();
  if (type === 1 && currentDepartmentId) {
    return { companyId: coId, departmentId: currentDepartmentId };
  } else if (type === 2) {
    return {companyId: coId,projectId: pjId };
  }
  return { companyId: coId };
};

export function getFutureList() {
  return request(`/dataview/api/corp/get?operatorInterCode=aqdp-gsc-wl6yfxqsfx`, {
    method: 'GET',
    params: formatDeptParam(),
  });
}

export function getRiskRadio(type: '1' | '2' | '3') {
  return request(`/dataview/api/corp/get?operatorInterCode=aqdp-gsc-fxgkqk-level`, {
    method: 'GET',
    params: { type, ...formatDeptParam() },
  });
}

export function getSafeSituation() {
  return request(`/dataview/api/corp/get?operatorInterCode=aqdp-gsc-aqts`, {
    method: 'GET',
    params: formatDeptParam(),
  });
}

export function getRiskEngineering() {
  return request(`/dataview/api/corp/get?operatorInterCode=aqdp-gsc-wdgcbt`, {
    method: 'GET',
    params: formatDeptParam(),
  });
}

export function getRiskManagement(type: '1' | '2' | '3') {
  return request(`/dataview/api/corp/get?operatorInterCode=aqdp-gsc-fxgkqk-xmlb`, {
    method: 'GET',
    params: { type, ...formatDeptParam() },
  });
}

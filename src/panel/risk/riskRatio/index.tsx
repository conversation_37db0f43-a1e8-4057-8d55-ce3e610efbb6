// 风险占比
import EmptyCenter from '@/components/EmptyCenter';
import opts from '@/panel/echartOptions';
import { useRequest } from 'ahooks';
import { Layout, Radio } from 'antd';
import ReactEcharts from 'echarts-for-react';
import React, { useMemo, useState } from 'react';
import { getRiskRadio } from '../api';

const OPTIONS = [
  {
    label: '实施中',
    value: '1',
  },
  {
    label: '未开始',
    value: '2',
  },
  {
    label: '已结束',
    value: '3',
  },
];

const RiskRatio = () => {
  const [selectType, setSelectType] = useState<'1' | '2' | '3'>('1');
  const { data = [] } = useRequest(
    async () => {
      const res = await getRiskRadio(selectType);
      return res?.data || [];
    },
    {
      refreshDeps: [selectType],
    },
  );

  return useMemo(() => {
    const echartsOption = {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)', // 显示占比百分数
      },
      legend: {
        orient: 'vertical',
        top:'center',
        right: '9%',
        icon: 'rect',
        itemWidth: 10, // 矩形宽度
        itemHeight: 10, // 矩形高度
      },
      color: ['#FF4D4F', '#FF9400', '#FFCD00', '#2478FF'], // 自定义颜色
      series: [
        {
          name: '风险占比',
          type: 'pie',
          radius: '70%', // 饼图的半径，可以设置为百分比或具体像素值
          center: ['45%', '50%'],
          label: {
            show: false,  // 隐藏连接线
          },
          data: data.map((item: any) => ({
            value: item.risk_num,
            name: item.danger_level,
          })),
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
            },
          },
        },
      ],
    };
    return (
      <Layout className="layoutPanelPm">
        <div style={{ position: 'absolute', right: 24, top: 16 }}>
          <Radio.Group
            size="small"
            options={OPTIONS}
            onChange={({ target: { value } }) => setSelectType(value)}
            defaultValue={selectType}
            optionType="button"
          />
        </div>
        {data ? (
          <Layout.Content>
            <ReactEcharts
              style={{ height: '98%' }}
              opts={opts}
              option={echartsOption}
              theme="clear"
            />
          </Layout.Content>
        ) : (
          <EmptyCenter description="暂无数据" />
        )}
      </Layout>
    );
  }, [selectType, data]);
};

export default RiskRatio;

.container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding-top: 16px;
  .item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: calc(50% - 8px);
    height: 90px;
    padding: 0 16px;
    background: #fbfbfb;
    border-radius: 4px;
    .left {
      .title {
        color: #000000a6;
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
      }
      .value {
        margin-top: 4px;
        color: #000000d9;
        font-weight: 500;
        font-size: 24px;
        line-height: 32px;
        text-align: left;

        .sub_title{
          font-size: 14px;
        }
      }
      .unit {
        color: #000000a6;
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
        text-align: left;
      }
    }
  }
  img {
    width: 64px;
    height: 64px;
  }
}

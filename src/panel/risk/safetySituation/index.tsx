import { useRequest } from 'ahooks';
import { Layout, Space } from 'antd';
import React from 'react';
import { getSafeSituation } from '../api';
import styles from './index.less';

const { Content } = Layout;

const SafetySituation = () => {
  const { data = [] } = useRequest(async () => {
    const res = await getSafeSituation();
    const array = [
      {
        title: '已签署责任书',
        value: res?.data['letterSigned'] || '--',
        subTitle: '',
      },
      {
        title: '责任书签署完成率',
        value: res?.data['letterSignedRate'] || '--',
        subTitle: '%',
      },
      {
        title: '今日新增隐患数',
        value: res?.data['newRiskToday'] || '--',
        subTitle: '',
      },
      {
        title: '近30日新增隐患数',
        value: res?.data['newRiskRecent'] || '--',
        subTitle: '',
      },
      {
        title: '近30日整改数量',
        value: res?.data['newFixRecent'] || '--',
        subTitle: '',
      },
      {
        title: '近30日整改率',
        value: res?.data['fixRate'] || '--',
        subTitle: '%',
      },
    ];
    return array;
  });

  return (
    <Layout className="layoutPanelPm">
      <Content>
        <div className={styles.container}>
          {data.map((item, index) => (
            <div className={styles.item}>
              <div className={styles.left}>
                <div className={styles.title}>{item.title}</div>
                <Space className={styles.value}>
                  {item.value} <div className={styles.sub_title}>{item.subTitle}</div>
                </Space>
              </div>
              <img src={require(`./assets/icon_${index + 1}.svg`)} alt="" />
            </div>
          ))}
        </div>
      </Content>
    </Layout>
  );
};

export default SafetySituation;

// 风险管控情况
import EmptyCenter from '@/components/EmptyCenter';
import { useRequest } from 'ahooks';
import { Layout, Radio,Table } from 'antd';
import React, { useMemo, useState } from 'react';
import { getRiskManagement } from '../api';

const OPTIONS = [
  {
    label: '实施中',
    value: '1',
  },
  {
    label: '未开始',
    value: '2',
  },
  {
    label: '已结束',
    value: '3',
  },
];

const RiskEngineering = () => {
  const [selectType, setSelectType] = useState<'1' | '2' | '3'>('1');
  const { data = [] } = useRequest(
    async () => {
      const res = await getRiskManagement(selectType);
      return res.data || [];
    },
    {
      refreshDeps: [selectType],
    },
  );
  const columns = [
    {
      title: '项目',
      dataIndex: 'project_name',
      ellipsis: true,
    },
    {
      title: '风险总数（个）',
      dataIndex: 'risk_num',
      ellipsis: true,
      width: 130,
    },
    {
      title: '重大风险数（个）',
      dataIndex: 'serious_risk_num',
      ellipsis: true,
      width: 130,
    },
  ];

  return useMemo(() => {
    return (
      <Layout className="layoutPanelPm">
        <div style={{ position: 'absolute', right: 24, top: 16 }}>
          <Radio.Group
            size="small"
            options={OPTIONS}
            onChange={({ target: { value } }) => setSelectType(value)}
            defaultValue={selectType}
            optionType="button"
          />
        </div>
        {data ? (
          <Layout.Content style={{overflow:'auto',padding:'16px 8px'}}>
           <Table
            rowClassName={(_, index) =>
              index % 2 === 0 ? 'homepanel-table-odd-row' : 'homepanel-table-even-row'
            }
            size="small"
            rowKey={(record: any) => `${record.department_name}-${record.project_name}`}
            dataSource={data}
            columns={columns}
            pagination={false}
          />
          </Layout.Content>
        ) : (
          <EmptyCenter description="暂无数据" />
        )}
      </Layout>
    );
  }, [selectType, data]);
};

export default RiskEngineering;

// 危大工程
import EmptyCenter from '@/components/EmptyCenter';
import opts from '@/panel/echartOptions';
import { useRequest } from 'ahooks';
import { Layout } from 'antd';
import ReactEcharts from 'echarts-for-react';
import React, { useMemo } from 'react';
import { getRiskEngineering } from '../api';

const RiskEngineering = () => {
  const { data = [] } = useRequest(
    async () => {
      const res = await getRiskEngineering();
      return res?.data || [];
    }
  );

  return useMemo(() => {
    const echartsOption = {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)', // 显示占比百分数
      },
      legend: {
        orient: 'vertical',
        top:'center',
        right: '9%',
        icon: 'rect',
        itemWidth: 10, // 矩形宽度
        itemHeight: 10, // 矩形高度
      },
      color: ['#FF4D4F', '#FF9400', '#FFCD00', '#2478FF'], // 自定义颜色
      series: [
        {
          name: '危大工程',
          type: 'pie',
          radius: '70%', // 饼图的半径，可以设置为百分比或具体像素值
          center: ['45%', '50%'],
          label: {
            show: false,  // 隐藏连接线
          },
          data,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
            },
          },
        },
      ],
    };
    return (
      <Layout className="layoutPanelPm">
        {data ? (
          <Layout.Content>
            <ReactEcharts
              style={{ height: '98%' }}
              opts={opts}
              option={echartsOption}
              theme="clear"
            />
          </Layout.Content>
        ) : (
          <EmptyCenter description="暂无数据" />
        )}
      </Layout>
    );
  }, [data]);
};

export default RiskEngineering;

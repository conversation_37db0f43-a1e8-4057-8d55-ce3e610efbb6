import React, { useEffect, useMemo, useState } from 'react';

import ReacrEcharts from 'echarts-for-react';
import { message } from 'antd';
import { problemStsService } from '@/services/bim';
import styles from './index.less';

const BimProblemTrend = () => {
  const [chartData, setChartData] = useState<any[]>([]);

  const getEchartsData = async () => {
    try {
      const response: any = await problemStsService({ statsType: 3 });
      if (response.success) {
        setChartData(response.data ?? []);
      } else {
        message.error(response.errMessage);
      }
    } catch {}
  };

  const options = useMemo(() => {
    let createProblems: number[] = [];
    let fixProblems: number[] = [];
    let offProblems: number[] = [];
    chartData.forEach((item) => {
      item?.valueDatas?.forEach((list: any) => {
        if (list.id == 1) {
          createProblems.push(list.value);
        }
        if (list.id == 2) {
          fixProblems.push(list.value);
        }
        if (list.id == 3) {
          offProblems.push(list.value);
        }
      });
    });
    return {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          // 坐标轴指示器，坐标轴触发有效
          type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
        },
      },
      legend: {
        data: ['创建问题', '修复问题', '关闭问题'],
      },
      grid: {
        top: 40,
        left: 16,
        right: '4%',
        containLabel: true,
      },
      dataZoom: [
        {
          type: 'slider',
          start: 0,
          end: 10,
          left: 20,
          right: 40,
          bottom: 10,
          showDetail: false,
        },
      ],
      xAxis: {
        type: 'category',
        boundaryGap: false,
        axisTick: {
          // x轴刻度
          show: false,
        },
        axisLine: { lineStyle: { color: 'rgb(162,162,162)' } },
        axisLabel: { color: 'rgb(162,162,162)' },
        splitLine: {
          lineStyle: { color: 'rgb(229,229,229)' },
        },
        data: chartData.map((item) => item.name),
      },
      yAxis: {
        type: 'value',
        axisTick: {
          // x轴刻度
          show: false,
        },
        axisLine: { show: false },
        axisLabel: { color: 'rgb(97,97,97)' },
      },
      series: [
        {
          name: '创建问题',
          type: 'line',
          stack: 'problem1',
          data: createProblems,
          itemStyle: {
            normal: { color: '#f4664a' },
          },
        },
        {
          name: '修复问题',
          type: 'line',
          stack: 'problem2',
          data: fixProblems,
          itemStyle: {
            normal: { color: '#faad14' },
          },
        },
        {
          name: '关闭问题',
          type: 'line',
          stack: 'problem3',
          data: offProblems,
          itemStyle: {
            normal: { color: '#30bf78' },
          },
        },
      ],
    };
  }, [chartData]);

  useEffect(() => {
    getEchartsData();
  }, []);

  return (
    <div className={styles.bimProblemTrend}>
      <ReacrEcharts option={options} />
    </div>
  );
};

export default BimProblemTrend;

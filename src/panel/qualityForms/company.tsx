import { Progress, Radio, Spin } from 'antd';
import React, { useEffect, useState } from 'react';
import styles from './index.less';

// assets
import { getCompanyStatsApi, getPageListApi } from './assets/api';
import { headerConfigs } from './assets/configs';

const Company = () => {
  const [info, setInfo] = useState<any>({});
  const [params, setParams] = useState<{
    current: number;
    size: number;
    queryType: '1' | '2';
    rateSort: 'desc' | 'asc';
    boardFlag: boolean; // 是否是看板
  }>({
    current: 1,
    size: 10,
    queryType: '1',
    rateSort: 'desc',
    boardFlag: true,
  });

  const [dataList, setDataList] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(false);

  // 获取统计数据
  const getCompanyStats = () => {
    getCompanyStatsApi({}).then((res) => {
      if (res?.success && res?.data) {
        setInfo(res.data);
      }
    });
  };
  useEffect(() => {
    getCompanyStats();
  }, []);
  // 获取列表
  const getPageList = () => {
    setLoading(true);
    getPageListApi(params)
      .then((res) => {
        if (res?.success && res?.data) {
          const { records = [] } = res.data || {};
          setDataList(records);
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };
  useEffect(() => {
    getPageList();
  }, [params]);

  const onChangeRadio = (e: any) => {
    const value = e.target.value;
    setParams((prev) => ({
      ...prev,
      rateSort: value,
    }));
  };

  const renderStatistics = () => (
    <section className={styles.statistics}>
      <section className={styles.item}>
        <h6>
          <span>总进度</span>
        </h6>
        <aside>
          <span className={styles.count}>{info?.totalAcceptRate || 0}</span>
          <span>%</span>
        </aside>
      </section>
      {headerConfigs.map((el) => (
        <section key={el.key} className={styles.item}>
          <h6>
            <span>{el.label}</span>
          </h6>
          <aside>
            <span className={styles.count}>{info?.[el.key] || 0}</span>
            <span>%</span>
          </aside>
        </section>
      ))}
    </section>
  );
  const renderList = () => (
    <>
      <section className={styles.title}>
        <span className={styles.text}>验收进度</span>
        <Radio.Group value={params.rateSort} onChange={onChangeRadio}>
          <Radio.Button value={'desc'}>升序</Radio.Button>
          <Radio.Button value={'asc'}>降序</Radio.Button>
        </Radio.Group>
      </section>
      <section className={styles.dataList} style={{ height: 180 }}>
        {!loading ? (
          dataList.map((el) => (
            <section key={el.projectId} className={styles.item}>
              <aside className={styles.text}>
                <span>{el?.projectName || ''}</span>
                <span>{el?.rate || 0}%</span>
              </aside>
              <Progress percent={el?.rate || 0} showInfo={false} />
            </section>
          ))
        ) : (
          <section style={{ textAlign: 'center' }}>
            <Spin />
          </section>
        )}
      </section>
    </>
  );

  return (
    <div className={styles.content}>
      {renderStatistics()}
      {renderList()}
    </div>
  );
};

export default Company;

const { request } = window.PMHOME;

// 获取企业统计
export async function getCompanyStatsApi(data: any) {
  return request(`/quality-forms/api/stats/getCompanyStats`, {
    method: 'POST',
    data,
  });
}

// 首页验收情况
export async function getPageListApi(data: {
  current: number;
  size: number;
  queryType: '1' | '2';
  rateSort: 'desc' | 'asc';
  boardFlag: boolean; // 是否是看板
}) {
  return request(`/quality-forms/api/stats/getProjectProgressPageList`, {
    method: 'POST',
    data,
  });
}

// 获取项目统计
export async function getProjectStatsApi(data: any) {
  return request(`/quality-forms/api/stats/getProjectStats`, {
    method: 'POST',
    data,
  });
}

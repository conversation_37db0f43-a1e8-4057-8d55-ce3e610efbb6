.content {
  .statistics {
    display: flex;
    .item {
      flex: 1;
      font-size: 14px;
      text-align: center;
      .count {
        margin-right: 8px;
        font-weight: 500;
        font-size: 24px;
      }
    }
  }
}
.title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 10px 0;
  .text {
    position: relative;
    padding-left: 12px;
    &:before {
      position: absolute;
      top: 0;
      left: 0;
      width: 4px;
      height: 22px;
      background-color: #2478ff;
      content: '';
    }
  }
}
.dataList {
  overflow-y: auto;
  .item {
    margin-bottom: 12px;
    .text {
      display: flex;
      justify-content: space-between;
    }
  }
}

.wrap {
  display: flex;
  .left {
    flex: 1;
    margin-right: 12px;
    padding-right: 12px;
    border-right: 1px solid #e5e5e5;
    .item {
      display: flex;
      padding: 30px 0;
      img {
        flex-shrink: 0;
        width: 58px;
        height: 58px;
        margin-right: 12px;
      }
      .count {
        margin-right: 8px;
        font-weight: 500;
        font-size: 24px;
      }
    }
  }
  .right {
    display: inherit;
    flex: 2;
    flex-wrap: wrap;
    .item {
      display: flex;
      width: 50%;
      padding: 20px 0;
      font-size: 14px;
      img {
        flex-shrink: 0;
        width: 50px;
        height: 50px;
      }
      .item_content {
        margin-left: 12px;
        .count {
          margin-right: 8px;
          font-weight: 500;
          font-size: 24px;
        }
      }
    }
  }
}

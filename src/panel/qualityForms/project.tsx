import { Progress } from 'antd';
import React, { useEffect, useState } from 'react';
import styles from './index.less';
// assets
import { getProjectStatsApi } from './assets/api';
import { headerConfigs } from './assets/configs';

const Project = () => {
  const [info, setInfo] = useState<any>({});

  const getProjectStats = () => {
    getProjectStatsApi({}).then((res) => {
      if (res?.success && res?.data) {
        setInfo(res.data);
      }
    });
  };
  useEffect(() => {
    getProjectStats();
  }, []);

  return (
    <div>
      <section className={styles.dataList}>
        <section className={styles.item}>
          <aside className={styles.text}>
            <span>总进度</span>
            <span>{info?.totalAcceptRate || 0}%</span>
          </aside>
          <Progress percent={info?.totalAcceptRate || 0} showInfo={false} />
        </section>
      </section>
      <section className={styles.wrap}>
        <section className={styles.left}>
          <section className={styles.item}>
            <img src={require('./assets/images/达标率.png')} alt="" />
            <section>
              <aside>验收通过数</aside>
              <aside>
                <span className={styles.count}>{info?.acceptNum || 0}</span>
              </aside>
            </section>
          </section>
          <section className={styles.item}>
            <img src={require('./assets/images/合同.png')} alt="" />
            <section>
              <aside>验收总数</aside>
              <aside>
                <span className={styles.count}>{info?.totalNum || 0}</span>
              </aside>
            </section>
          </section>
        </section>
        <section className={styles.right}>
          {headerConfigs.map((el) => (
            <section key={el.key} className={styles.item}>
              <img src={el.imagePath} alt="" />
              <section className={styles.item_content}>
                <aside>{el.label}</aside>
                <aside>
                  <span className={styles.count}>{info?.[el.key] || 0}</span>
                  <span>%</span>
                </aside>
                <aside>验收数：{info?.[el.count] || 0}</aside>
              </section>
            </section>
          ))}
        </section>
      </section>
    </div>
  );
};

export default Project;

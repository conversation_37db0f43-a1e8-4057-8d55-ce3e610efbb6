import React from 'react';
import styles from './index.less'
interface IbarProps {
  firstColor?: string;
  secondColor?: string;
  firstWidth?: number;
  secondWidth?: number;
}

export default ((props: IbarProps) => {
  const {
    firstColor, secondColor, firstWidth, secondWidth
  } = props;
  return (
    <div className={styles.relative}>
      <p className={styles.grey} style={{backgroundColor: '#E6E9EC', borderRadius: '5px'}}/>
      <p className={styles.grey} style={{backgroundColor: `${firstColor}`, position: 'absolute', left: 0, top: 0, width: `${firstWidth}%`, borderRadius: secondWidth ? '5px 0 0 5px' : '5px'}}/>
      <p className={styles.grey} style={{backgroundColor: `${secondColor}`, position: 'absolute', left: `${firstWidth}%`, top: 0, width: `${Math.abs(secondWidth as number)}%`, borderRadius: '0 5px 5px 0'}}/>
    </div>
  );
});

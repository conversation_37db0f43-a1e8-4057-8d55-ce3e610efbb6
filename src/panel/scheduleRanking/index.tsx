import React, { useState } from 'react';
import { Layout } from 'antd';
import ourStyle from './index.less';
import { getScheduleRankDelay } from '@/services/progress'
import { useEffectOnce } from 'react-use';
import More from '@/components/more';
import EmptyCenter from '@/components/EmptyCenter';
import PaintingBar from './PaintingBar'
// @ts-ignore
import Scrollbars from 'react-custom-scrollbars';
const {Content} = Layout
interface Ires {
  code?: number;
  data: any;
  message?: string;
  success?: boolean;
  list?: any;
  errorMsg?: string
}
export default () => {
  const [rankingArr, setrRankingArr] = useState([]);


  useEffectOnce(() => {
    ranking()
  })

  function ranking() {
    getScheduleRankDelay({page: 1, pageSize: 150}).then((res: Ires) => {
      if (res.success) {
        const { data: {dataList} } = res;
        setrRankingArr(dataList)
      }
    });
  }

  function goDetail(projectId: number) {
    const url = `${window.location.protocol}//${window.location.host}/console/schedule-plan/#/schedule-plan/proDetails?pjId=${projectId}`
    window.location.replace(url)
  }

  const renderThumb = (payload: any) => {   // 设置滚动条的样式
    const thumbStyle = {
      backgroundColor: '#000',
      borderRadius: '6px',
      opacity: 0,
      ...payload
    }
    return <div style={{...thumbStyle }}/>
  }

  return (
    <Layout className='layoutPanelPm'>
      <Content style={{display: 'flex'}}>
        <More jumpUrl="/console/schedule-plan/#/schedule-plan/" />
        <div className={ourStyle.over_y}>
          <Scrollbars renderThumbVertical={() => renderThumb({ right: '4px',  width: '8px'})}>
            {
              rankingArr.map((x: any) => {
                return <div className={ourStyle.warp_div_son} key={`rank-${x.projectId}`}>
                  <div style={{width: '150px'}} className={ourStyle.ellipsis} onClick={() => goDetail(x.projectId)}><span title={x.projectName}>{x.projectName}</span></div>
                  <div style={{flex: 1,marginTop: '6px'}}>
                    <PaintingBar firstWidth={x.scale1} secondWidth={x.scale2} firstColor='#00C58E' secondColor='#FF8549' />
                  </div>
                  <div style={{width: '180px'}}>
                    <span style={{paddingLeft: '8px', width: '70px',display: 'inline-block'}}>{x.scale1} %</span>
                    {x.schedule < 0 ? <span className={ourStyle.delay_btn}>滞后{Math.abs(x.schedule as number)}天</span> : <span className={`${ourStyle.delay_btn} ${ourStyle.nomal_btn}`}>正常</span>}
                  </div>
                </div>
              })
            }
            {
              rankingArr.length === 0 && <EmptyCenter description="暂无数据" />
            }
          </Scrollbars>
        </div>
      </Content>
    </Layout>
  )
}

/*
 * @Author: wendy
 * @Date: 2020-08-26 09:45:49
 * @LastEditors: chenhuan
 * @LastEditTime: 2024-10-15 13:41:56
 * @Description:
 */
import { dynamic } from 'umi';

export interface IPanelComponent {
  panel_message_center: '';
}
export interface IPanelParam {
  system: boolean;
  min: 1 | 2 | 3 | 4 | 2.5;
  width: 1 | 2 | 3 | 4 | 2.5;
}

export const size: any = {
  1: 1 / 4,
  2: 1 / 3,
  3: 2 / 3,
  4: 1,
  2.5: 1 / 2,
};
export enum SizeEnum {
  QUARTER = 1, // 四分之一
  THIRD, // 三分之一
  TWOPART, // 三分之二
  FULLSCREEN, // 全屏
  HALF = 2.5, // 二分之一
}

interface IObject {
  [key: string]: any;
}
/*
 * system 是否系统组件
 * min 最小宽度
 * width 默认宽度  {1: 4x, 2: 2x, 3: 3x, 4: 1, 2.5: 2.5x}
 * */
const panels: IObject = {
  panel_message_notice: {
    width: SizeEnum.THIRD,
    Component: dynamic({
      loader: () => import(/* webpackChunkName: "panel_message_notice" */ '@/panel/notice'),
    }),
  },
  // 项目统计 panel_panshi_project_stat
  panel_panshi_project_stat: {
    width: SizeEnum.HALF,
    min: SizeEnum.HALF,
    Component: dynamic({
      loader: () => import('@/panel/projectOverview'),
    }),
  },
  panel_message_news: {
    width: SizeEnum.THIRD,
    Component: dynamic({
      loader: () => import(/* webpackChunkName: "panel_message_news" */ '@/panel/news'),
    }),
  },
  panel_person_attendance: {
    width: SizeEnum.HALF,
    Component: dynamic({
      loader: () =>
        import(/* webpackChunkName: "panel_person_attendance" */ '@/panel/person/attendance'),
    }),
  },
  panel_person_attendance_rank: {
    width: SizeEnum.HALF,
    Component: dynamic({
      loader: () =>
        import(
          /* webpackChunkName: "panel_person_attendance_rank" */ '@/panel/person/attendanceRank'
        ),
    }),
  },
  panel_person_site_rank: {
    width: SizeEnum.HALF,
    Component: dynamic({
      loader: () =>
        import(/* webpackChunkName: "panel_person_site_rank" */ '@/panel/person/panelPersonSite'),
    }),
  },
  panel_env_monitor: {
    width: SizeEnum.HALF,
    min: 2,
    Component: dynamic({
      loader: () => import(/* webpackChunkName: "panel_envMonitor" */ '@/panel/monitor'),
    }),
  },
  panel_devices_Monitoring: {
    width: SizeEnum.HALF,
    Component: dynamic({
      loader: () =>
        import(/* webpackChunkName: "panel_devices_Monitoring" */ '@/panel/devicesMonitor'),
    }),
  },
  panel_monitor: {
    width: SizeEnum.HALF,
    Component: dynamic({
      loader: () => import(/* webpackChunkName: "panel_video_monitor" */ '@/panel/videoMonitor'),
    }),
  },
  panel_project_total: {
    width: SizeEnum.HALF,
    Component: dynamic({
      loader: () => import(/* webpackChunkName: "panel_project_total" */ '@/panel/projectTotal'),
    }),
  },
  panel_project_schedule: {
    width: SizeEnum.HALF,
    min: SizeEnum.HALF,
    Component: dynamic({
      loader: () =>
        import(/* webpackChunkName: "panel_project_schedule" */ '@/panel/projectSchedule'),
    }),
  },
  panel_project_progress: {
    width: SizeEnum.HALF,
    Component: dynamic({
      loader: () =>
        import(/* webpackChunkName: "panel_project_progress" */ '@/panel/projectProgress'),
    }),
  },
  panel_quality: {
    width: SizeEnum.HALF,
    min: 2,
    Component: dynamic({
      loader: () => import(/* webpackChunkName: "panel_quality" */ '@/panel/quality'),
    }),
  },
  panel_security: {
    width: SizeEnum.HALF,
    min: 2,
    Component: dynamic({
      loader: () => import(/* webpackChunkName: "panel_security" */ '@/panel/security'),
    }),
  },
  panel_project_warning: {
    min: SizeEnum.HALF,
    width: SizeEnum.HALF,
    Component: dynamic({
      loader: () =>
        import(/* webpackChunkName: "panel_project_warning" */ '@/panel/projectProgress/warning'),
    }),
  },
  panel_company_schedule_ranking: {
    width: SizeEnum.HALF,
    min: SizeEnum.HALF,
    Component: dynamic({
      loader: () =>
        import(/* webpackChunkName: "panel_company_schedule_ranking" */ '@/panel/scheduleRanking'),
    }),
  },
  panel_company_schedule_controlled: {
    width: SizeEnum.HALF,
    min: SizeEnum.HALF,
    Component: dynamic({
      loader: () =>
        import(
          /* webpackChunkName: "panel_company_schedule_ranking" */ '@/panel/scheduleControlled'
        ),
    }),
  },
  panel_company_schedule_manage: {
    width: SizeEnum.HALF,
    min: SizeEnum.HALF,
    Component: dynamic({
      loader: () =>
        import(/* webpackChunkName: "panel_company_schedule_manage" */ '@/panel/scheduleMange'),
    }),
  },
  panel_epidemic_statistics: {
    //健康上报
    width: SizeEnum.HALF,
    Component: dynamic({
      loader: () =>
        import(/* webpackChunkName: "panel_person_attendance" */ '@/panel/epidemicSolution/index'),
    }),
  },
  panel_epidemic_manage: {
    //防疫管理
    width: SizeEnum.HALF,
    Component: dynamic({
      loader: () =>
        import(/* webpackChunkName: "panel_person_attendance" */ '@/panel/epidemicManage/index'),
    }),
  },
  panel_subregion_attendance: {
    //分区现场人数统计
    width: SizeEnum.HALF,
    Component: dynamic({
      loader: () =>
        import(/* webpackChunkName: "panel_person_attendance" */ '@/panel/subSitePerson/index'),
    }),
  },
  panel_mark_room_monitor: {
    //标养室监测
    width: SizeEnum.HALF,
    min: 2,
    Component: dynamic({
      loader: () =>
        import(/* webpackChunkName: "panel_mark_room_monitor" */ '@/panel/markRoomMonitor/index'),
    }),
  },
  // 20220505  DYX add
  // panel_project_notice:{ // 项目公告
  //   width: SizeEnum.HALF,
  //   Component:dynamic({
  //     loader:()=> import(/* webpackChunkName: "panel_project_notice" */'@/panel/projectNotice/index')
  //   })
  // },
  panel_bim_hibim_scheduler: {
    // 工作计划
    width: SizeEnum.HALF,
    Component: dynamic({
      loader: () => import(/* webpackChunkName: "panel_work_plan" */ '@/panel/workPlan/index'),
    }),
  },
  panel_bim_hibim_prblems_mime: {
    // 我的问题
    width: SizeEnum.HALF,
    Component: dynamic({
      loader: () => import(/* webpackChunkName: "panel_my_question" */ '@/panel/myQuestion/index'),
    }),
  },
  panel_bim_hibim_problem_states: {
    // 问题统计
    width: SizeEnum.HALF,
    Component: dynamic({
      loader: () =>
        import(
          /* webpackChunkName: "panel_problems_statistical" */ '@/panel/problemsStatistical/index'
        ),
    }),
  },
  panel_bim_hibim_approval_states: {
    // 审计统计
    width: SizeEnum.HALF,
    Component: dynamic({
      loader: () =>
        import(
          /* webpackChunkName: "panel_problems_statistical" */ '@/panel/auditStatistics/index'
        ),
    }),
  },
  panel_bim_hibim_approval_mime: {
    // 我的审批
    width: SizeEnum.HALF,
    Component: dynamic({
      loader: () =>
        import(/* webpackChunkName: "panel_problems_statistical" */ '@/panel/myApproval/index'),
    }),
  },
  panel_new_person_attendance: {
    //新出入考勤  222
    width: SizeEnum.HALF,
    Component: dynamic({
      loader: () =>
        import(
          /* webpackChunkName: "panel_new_person_attendance" */ '@/panel/person/newAttendance'
        ),
    }),
  },
  // 出勤统计（企业级）
  panel_attendance_statistic_co: {
    width: SizeEnum.HALF,
    Component: dynamic({
      loader: () =>
        import(
          /* webpackChunkName: "panel_attendance_statistic" */ '@/panel/person/AttendanceStatistic/Co'
        ),
    }),
  },
  // 出勤统计（项目级）
  panel_attendance_statistic_pj: {
    width: SizeEnum.HALF,
    Component: dynamic({
      loader: () =>
        import(
          /* webpackChunkName: "panel_attendance_statistic" */ '@/panel/person/AttendanceStatistic/Pj'
        ),
    }),
  },
  panel_person_new_site_rank: {
    //新今日现场人数排行  222
    width: SizeEnum.HALF,
    Component: dynamic({
      loader: () =>
        import(
          /* webpackChunkName: "panel_person_new_site_rank" */ '@/panel/person/newPanelPersonSite'
        ),
    }),
  },
  panel_subregion_new_attendance: {
    //新分区现场人数实时统计 222
    width: SizeEnum.HALF,
    Component: dynamic({
      loader: () =>
        import(
          /* webpackChunkName: "panel_subregion_new_attendance" */ '@/panel/subSitePersonNew/index'
        ),
    }),
  },
  panel_epidemic_new_manage: {
    //新防疫管理
    width: SizeEnum.HALF,
    Component: dynamic({
      loader: () =>
        import(/* webpackChunkName: "panel_person_attendance" */ '@/panel/newEpidemicManage/index'),
    }),
  },
  panel_bim_file_upload: {
    // bim - 文件上传统计
    width: SizeEnum.HALF,
    Component: dynamic({
      loader: () =>
        import(/* webpackChunkName: "panel_bim_file_upload" */ '@/panel/bimFileUpload/index'),
    }),
  },
  panel_bim_doc_statistics: {
    // bim - 文件按类型统计
    width: SizeEnum.HALF,
    Component: dynamic({
      loader: () =>
        import(
          /* webpackChunkName: "panel_problems_statistical" */ '@/panel/bimDocStatistics/index'
        ),
    }),
  },
  panel_bim_problem_trend: {
    // bim - 问题趋势
    width: SizeEnum.HALF,
    Component: dynamic({
      loader: () =>
        import(/* webpackChunkName: "panel_bim_file_upload" */ '@/panel/bimProblemTrend/index'),
    }),
  },
  panel_bim_upload_situation: {
    // bim - 企业卡片 - 文件上传情况
    width: SizeEnum.HALF,
    Component: dynamic({
      loader: () =>
        import(/* webpackChunkName: "panel_bim_file_upload" */ '@/panel/bimUploadSituation/index'),
    }),
  },
  panel_bim_problem_track: {
    // bim - 企业卡片 - 问题追踪
    width: SizeEnum.HALF,
    Component: dynamic({
      loader: () =>
        import(/* webpackChunkName: "panel_bim_file_upload" */ '@/panel/bimProblemTrack/index'),
    }),
  },
  panel_bim_problem_statistics: {
    // bim - 问题统计
    width: SizeEnum.HALF,
    Component: dynamic({
      loader: () =>
        import(
          /* webpackChunkName: "panel_problems_statistical" */ '@/panel/bimProblemStatistics/index'
        ),
    }),
  },
  panel_quality_forms: {
    // 质量验收
    width: SizeEnum.HALF,
    Component: dynamic({
      loader: () =>
        import(/* webpackChunkName: "panel_problems_statistical" */ '@/panel/qualityForms/index'),
    }),
  },
  panel_quality_goal: {
    // 质量目标
    width: SizeEnum.HALF,
    Component: dynamic({
      loader: () =>
        import(/* webpackChunkName: "panel_problems_statistical" */ '@/panel/qualityGoal/index'),
    }),
  },
  panel_quality_excellence: {
    // 质量创优
    width: SizeEnum.HALF,
    Component: dynamic({
      loader: () =>
        import(
          /* webpackChunkName: "panel_problems_statistical" */ '@/panel/qualityExcellence/index'
        ),
    }),
  },
  panel_safety_inspection: {
    // 安全检查
    width: SizeEnum.HALF,
    min: 2,
    Component: dynamic({
      loader: () =>
        import(/* webpackChunkName: "panel_security" */ '@/panel/safetyInspection/index'),
    }),
  },
  panel_safety_inspection_table: {
    // 安全检查 - table
    width: SizeEnum.HALF,
    min: 2,
    Component: dynamic({
      loader: () =>
        import(/* webpackChunkName: "panel_security" */ '@/panel/safetyInspection/tablePage'),
    }),
  },
  panel_quality_inspection: {
    // 质量检查
    width: SizeEnum.HALF,
    min: 2,
    Component: dynamic({
      loader: () =>
        import(/* webpackChunkName: "panel_security" */ '@/panel/qualityInspection/index'),
    }),
  },
  panel_quality_inspection_table: {
    // 质量检查 - table
    width: SizeEnum.HALF,
    min: 2,
    Component: dynamic({
      loader: () =>
        import(/* webpackChunkName: "panel_security" */ '@/panel/qualityInspection/tablePage'),
    }),
  },
  //生产管理
  panel_production_manage: {
    width: SizeEnum?.HALF,
    Component: dynamic({
      loader: () =>
        import(
          /* webpackChunkName: "panel_problems_statistical" */ '@/panel/productionManage/index'
        ),
    }),
  },
  // 协同建模任务-项目级
  panel_bim_collaboration_pm: {
    width: SizeEnum.HALF,
    Component: dynamic({
      loader: () =>
        import(/* webpackChunkName: "panel_bim_collaboration_pm" */ '@/panel/collaModeling/index'),
    }),
  },
  // 成团建模任务-项目级
  panel_bim_collaMember_pm: {
    width: SizeEnum.HALF,
    Component: dynamic({
      loader: () =>
        import(/* webpackChunkName: "panel_bim_collaboration_pm" */ '@/panel/collaMember/index'),
    }),
  },
  // 协同建模任务-企业级
  panel_bim_collaborationTask_cm: {
    width: SizeEnum.HALF,
    Component: dynamic({
      loader: () =>
        import(
          /* webpackChunkName: "panel_bim_collaboration_pm" */ '@/panel/collaborationTask/index'
        ),
    }),
  },
  // 数组资产使用-企业级
  panel_bim_digitalAssets_cm: {
    width: SizeEnum.HALF,
    Component: dynamic({
      loader: () =>
        import(/* webpackChunkName: "panel_bim_collaboration_pm" */ '@/panel/digitalAssets/index'),
    }),
  },
  // 各公司风险情况-企业级
  panel_branch_risk_cm: {
    width: SizeEnum.HALF,
    Component: dynamic({
      loader: () =>
        import(/* webpackChunkName: "panel_branch_risk_cm" */ '@/panel/riskSituation/index'),
    }),
  },
  // 各分公司重大风险情况-企业级
  panel_branch_major_risk_cm: {
    width: SizeEnum.HALF,
    Component: dynamic({
      loader: () =>
        import(/* webpackChunkName: "panel_branch_major_risk_cm" */ '@/panel/majorRiskSituation/index'),
    }),
  },
  // 检查覆盖率排行-企业级
  panel_hazard_analysis_cm: {
    width: SizeEnum.HALF,
    Component: dynamic({
      loader: () =>
        import(/* webpackChunkName: "panel_hazard_analysis_cm" */ '@/panel/hazardAnalyse/index'),
    }),
  },
  // 隐患整改情况
  panel_hazard_correction_cm: {
    width: SizeEnum.HALF,
    Component: dynamic({
      loader: () =>
        import(/* webpackChunkName: "panel_hazard_correction_cm" */ '@/panel/hazardCorrection/index'),
    }),
  },
  // 不同等级的隐患整改情况
  panel_hazard_correction_by_level_pm: {
    width: SizeEnum.HALF,
    Component: dynamic({
      loader: () =>
        import(/* webpackChunkName: "panel_hazard_correction_by_level_pm" */ '@/panel/hazardCorrectionByLevel/index'),
    }),
  },
  // 危大工程管控
  panel_high_risk_engineering_control_pm: {
    width: SizeEnum.HALF,
    Component: dynamic({
      loader: () =>
        import(/* webpackChunkName: "panel_high_risk_engineering_control_pm" */ '@/panel/riskEngineerControl/index'),
    }),
  },
  // 项目概况
  panel_project_overview2_pm: {
    width: SizeEnum.HALF,
    Component: dynamic({
      loader: () =>
        import(/* webpackChunkName: "panel_project_overview2_pm" */ '@/panel/overViewPm/index'),
    }),
  },

  // 未来 6 个月风险趋势分析 企业/分公司/项目
  panel_future_risk_trend: {
    width: SizeEnum.HALF,
    Component: dynamic({
      loader: () =>
        import(/* webpackChunkName: "panel_future_risk_trend" */ '@/panel/risk/futureRiskTrend'),
    }),
  },
  // 风险占比 企业
  panel_risk_ratio_cm: {
    width: SizeEnum.HALF,
    Component: dynamic({
      loader: () => import(/* webpackChunkName: "panel_risk_ratio_cm" */ '@/panel/risk/riskRatio'),
    }),
  },

  // 隐患分析 企业/分公司/项目
  panel_hazard_analysis: {
    width: SizeEnum.HALF,
    Component: dynamic({
      loader: () =>
        import(/* webpackChunkName: "panel_hazard_analysis" */ '@/panel/hazard/hazardAnalysis'),
    }),
  },

  // 安全态势  企业/分公司/项目
  panel_safety_situation: {
    width: SizeEnum.HALF,
    Component: dynamic({
      loader: () =>
        import(/* webpackChunkName: "panel_safety_situation" */ '@/panel/risk/safetySituation'),
    }),
  },
  // 危大工程  企业/分公司/项目
  panel_high_risk_engineering: {
    width: SizeEnum.HALF,
    Component: dynamic({
      loader: () =>
        import(
          /* webpackChunkName: "panel_high_risk_engineering" */ '@/panel/risk/riskEngineering'
        ),
    }),
  },
  // 风险管控情况 分公司
  panel_risk_management: {
    width: SizeEnum.HALF,
    Component: dynamic({
      loader: () =>
        import(
          /* webpackChunkName: "panel_risk_management" */ '@/panel/risk/riskManagement'
        ),
    }),
  },

  // 常用应用  分公司
  panel_common_apps: {
    width: SizeEnum.HALF,
    Component: dynamic({
      loader: () => import(/* webpackChunkName: "panel_common_apps" */ '@/panel/commonApps'),
    }),
  },
  // 待办任务
  panel_todo_task: {
    width: SizeEnum.TWOPART,
    min: SizeEnum.TWOPART,
    Component: dynamic({
      loader: () => import(/* webpackChunkName: "panel_todo_task" */ '@/panel/todoTask'),
    }),
  },
  /**随手拍 */
  casual_snapshots: {
    width: SizeEnum.TWOPART,
    min: SizeEnum.TWOPART,
    Component: dynamic({
      loader: () => import(/* webpackChunkName: "casual_snapshots" */ '@/panel/casualSnapshots'),
    }),
  }
};
export default panels;

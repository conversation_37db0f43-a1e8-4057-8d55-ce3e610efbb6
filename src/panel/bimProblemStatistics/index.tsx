import React, { useEffect, useState } from 'react';

import ReacrEcharts from 'echarts-for-react';
import { problemStsApi } from '@/services/bim';

let observer: MutationObserver | undefined;
let oldWidth = 0;

const BimProblemStatistics = () => {
  const [doughnutData, setDoughnutData] = useState<any>({ text: '', subtext: '', data: [] });
  const [hide, setHide] = useState<boolean>(false);

  const getChartInfo = () => {
    problemStsApi({ statsType: 0 }).then((res) => {
      if (res.success) {
        let sum = 0,
          color: string[] = [],
          subtext;
        const data = res.data.map((item: any) => {
          sum += item.value;
          if (item.id === 0) {
            color.push('#F4664A');
            return {
              name: '修复中',
              value: item.value,
            };
          } else if (item.id === 1) {
            color.push('#FAAD14');
            return {
              name: '审核中',
              value: item.value,
            };
          } else if (item.id === 2) {
            color.push('#30BF78');
            return {
              name: '已关闭',
              value: item.value,
            };
          }
        });
        if (sum > 10000) {
          subtext = `${sum / 10000}{small|万}`;
        } else {
          subtext = `${sum}{small|个}`;
        }
        // initDoughnutChart({ text: '总问题数', subtext, data });
        setDoughnutData({ text: '总问题数', subtext: `${sum}`, data, color });
      }
    });
  };

  /**
   * 生成环形图
   * @params hide 是否隐藏图例
   */
  const initDoughnutChart = (hide: boolean) => {
    // let myChart = echarts.init(document.getElementById('problemStatisticChart')!);

    let option: any = {
      tooltip: {
        trigger: 'item',
        formatter(params: any) {
          let value;
          if (params.value > 10000) {
            value = `${params.value / 10000}万`;
          } else {
            value = `${params.value}个`;
          }
          return `${params.marker} ${params.name}：${params.percent}%&emsp;&emsp;${value}`;
        },
      },
      title: {
        show: true,
        text: doughnutData.text,
        subtext: doughnutData.subtext,
        top: '41.5%',
        left: 'center',
        textStyle: {
          fontWeight: 300,
          fontSize: 12,
        },
        subtextStyle: {
          fontWeight: 600,
          fontSize: 24,
          color: '#454545',
          rich: {
            small: {
              // color: 'red',
              fontSize: 14,
              height: 20,
              verticalAlign: 'bottom',
            },
          },
        },
      },
      // legend: {
      //   orient: 'vertical',
      //   top: 'center',
      //   right: '5%',
      //   icon: 'circle',
      // },
      series: [
        {
          type: 'pie',
          radius: ['55%', '85%'],
          avoidLabelOverlap: false,
          label: {
            show: true,
            position: 'inside',
            // formatter: '{d}%',
            formatter(params: any) {
              let value;
              if (params.percent > 5) {
                value = `${params.percent}%`;
              } else {
                value = ``;
              }
              return value;
            },
          },
          emphasis: {
            // 鼠标悬浮
            label: {
              show: true,
              fontSize: '20',
              fontWeight: 'bold',
            },
          },
          labelLine: {
            show: false,
          },
          color: doughnutData.color,
          data: doughnutData.data,
        },
      ],
    };
    let width = document.getElementById('problemStatisticChart')?.clientWidth ?? 0;
    if (!hide && width >= 480) {
      option['legend'] = {
        orient: 'vertical',
        top: 'center',
        right: '3%',
        icon: 'circle',
      };
    }
    // option && myChart.setOption(option);
    return option;
  };

  //浏览器窗口大小改变
  const resizeChange = () => {
    let width = document.getElementById('problemStatisticChart')?.clientWidth ?? 0;

    if (width === oldWidth) return;
    oldWidth = width;

    if (width < 480) {
      // initDoughnutChart(true);
      setHide(true);
    } else {
      setHide(false);
      // initDoughnutChart(false);
    }
  };

  useEffect(() => {
    getChartInfo();
  }, []);

  useEffect(() => {
    // 监听
    let MutationObserver = window.MutationObserver;
    let element: any = document.getElementById('problemStatisticChart');
    observer = new MutationObserver(resizeChange);

    observer.observe(element as Node, {
      attributes: true,
      childList: true,
      subtree: true,
    });

    // 销毁
    return () => {
      observer?.disconnect();
    };
  }, []);

  return (
    <div
      id="problemStatisticChart"
      style={{
        width: '100%',
        height: '100%',
      }}
    >
      {/**@ts-ignore */}
      <ReacrEcharts option={initDoughnutChart(hide)} notMerge={true} />
    </div>
  );
};

export default BimProblemStatistics;

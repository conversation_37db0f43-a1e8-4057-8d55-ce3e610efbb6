// 新闻中心
import More from '@/components/more';
import { getEvents } from '@/services/noticeAnnounce';
import { Layout } from 'antd';
import React, { useEffect, useState } from 'react';
import NewsOrEvent from './components/NewsOrEvent';

export default () => {
  // 新闻中心
  const [selectEventIndex, setEventIndex] = useState<number>(0);
  const [eventImgUrl, setEventImgUrl] = useState<string>('');
  const [selectEventItem, setSelectEventItem] = useState<any>({});
  const [eventList, setEventList] = useState<any[]>([]);

  // 获取新闻中心列表
  const getList = () => {
    getEvents({pageSize: 10, current: 1}).then((res: any) => {
      let records: any[] = [];
      if (res?.data) {
        records = res?.data?.records;
      }

      setEventList(records);
      if (records?.length) {
        setSelectEventItem(records[0]);
        setEventImgUrl(records[0].cover);
      }
    });
  };

  // 查看新闻中心详情
  const seeEventDetail = (item: any, index: number) => {
    setSelectEventItem(item);
    setEventIndex(index);
    setEventImgUrl(item?.cover);
  };

  useEffect(() => {
    getList();
  }, []);

  return (
    <Layout className="layoutPanelPm">
      <Layout.Content>
        <More
          content="更多"
          onClick={() => {
            //跳转至新闻中心子应用
            window.location.href = 'https://aq.cctc.cn/console/news-center/News';
          }}
        />
        <NewsOrEvent
          imgUrl={eventImgUrl}
          dataSource={eventList}
          setImgUrl={setEventImgUrl}
          seeDetail={seeEventDetail}
          selectItem={selectEventItem}
          selectIndex={selectEventIndex}
        />
      </Layout.Content>
    </Layout>
  );
};

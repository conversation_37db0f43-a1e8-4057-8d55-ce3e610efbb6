import React, { useState } from 'react';
import { Image, Layout } from 'antd';
import EmptyCenter from '@/components/EmptyCenter';
import DetailModal from '../DetailModal';
import Styles from './index.less';
const { Content } = Layout;

const NewsOrEvent = (props: any) => {
  const { imgUrl, dataSource, setImgUrl, seeDetail, selectItem, selectIndex } = props;
  const [showDetail, setShowDetail] = useState<boolean>(false);
  const [rowData, setRowData] = useState<any>({});
  return (
    <div>
      <Layout className="layoutPanelPm">
        <Content style={{ display: 'flex' }}>
          <div className={Styles.noticeContainer}>
            {dataSource?.length ? (
              <>
                <div className={Styles.noticeImg}>
                  <Image width={'100%'} height={268} src={imgUrl} preview={false} />
                </div>
                <div
                  className={Styles.noticeListContainer}
                  onMouseLeave={() => setImgUrl(selectItem?.cover)}
                >
                  {dataSource?.map((item: any, index: number) => {
                    return (
                      <div
                        className={Styles.noticeItem}
                        style={{
                          backgroundColor: index === selectIndex ? '#F5F6F7' : '',
                        }}
                        key={index}
                        onClick={() => {
                          seeDetail(item, index);
                          setRowData(item);
                          setShowDetail(true);
                        }}
                        onMouseEnter={() => {
                          setImgUrl(item?.cover);
                        }}
                      >
                        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                          <div className={Styles.noticeItemTitle} title={item.title}>{item.title}</div>
                          <div className={Styles.projectTitle}>{item.projectName}</div>
                        </div>
                        <div className={Styles.noticeItemText}>{item.publishDate}</div>
                      </div>
                    );
                  })}
                </div>
              </>
            ) : (
              <EmptyCenter description="暂无数据" />
            )}
          </div>
        </Content>
      </Layout>
      <DetailModal
        visible={showDetail}
        onCancel={() => {
          setShowDetail(false);
        }}
        rowData={rowData}
      />
    </div>
  );
};

export default NewsOrEvent;

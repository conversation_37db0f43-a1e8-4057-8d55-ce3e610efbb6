.noticeContainer {
    display: flex;
    flex: 1;
    width: 100%;

    .noticeImg {
        // max-width: 474px;
        height: 268px;
        width: 65%;
        overflow: hidden;
    }

    .noticeListContainer {
        flex: 1;
        width: 100%;
        height: 268px;
        overflow: auto;

        :hover {
            background-color: rgba(245, 246, 247, 1);
            cursor: pointer;
        }

        .noticeItem {
            width: 100%;
            height: 68px;
            padding: 12px 5px 12px 19px;

            .noticeItemTitle {
                display: -webkit-box;
                width: 100%;
                overflow: hidden;
                color: rgba(0, 0, 0, 0.85);
                font-weight: 700;
                font-size: 14px;
                font-family: PingFangSC-Medium;
                line-height: 22px;
                text-overflow: ellipsis;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
                white-space: pre-line;
                word-break: break-all;
                padding-bottom: 2px;
            }

            .projectTitle {
                display: -webkit-box;
                width: 20%;
                text-align: end;
                overflow: hidden;
                color: rgba(0, 0, 0, 0.85);
                font-weight: 500;
                font-size: 14px;
                font-family: PingFangSC-Medium;
                line-height: 22px;
                text-overflow: ellipsis;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
                white-space: pre-line;
                word-break: break-all;
                padding-bottom: 2px;
                margin-right: 20px;
            }

            .noticeItemText {
                padding-top: 2px;
                display: -webkit-box;
                // max-width: 429px;
                width: 100%;
                height: 22px;
                overflow: hidden;
                color: rgba(0, 0, 0, 0.65);
                font-weight: 400;
                font-size: 14px;
                font-family: PingFangSC-Regular;
                line-height: 22px;
                text-align: left;
                text-overflow: ellipsis;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
                // white-space: pre-line;
                word-break: break-all;
            }
        }
    }
}
import React from 'react';
import { Modal, Space, Carousel, Empty } from 'antd';
import FileListView from '../FileListView';
import { useRequest } from 'ahooks';
import { getEventsDetail } from '@/services/noticeAnnounce';
import styles from './index.less';

const DetailModal = (props) => {
  const { visible, onCancel, rowData } = props;
  const {
    data: { photos, files, title, publishDate, content, createName, projectName } = {},
  } = useRequest(
    async () => {
      if (!rowData?.id) return;
      if (!visible) return;
      const res = await getEventsDetail({ id: rowData?.id });
      return res?.data;
    },
    {
      refreshDeps: [rowData?.id, visible],
    },
  );

  return (
    <Modal
      title={'详情'}
      visible={visible}
      onCancel={onCancel}
      width={800}
      destroyOnClose
      maskClosable={false}
      footer={null}
      bodyStyle={{
        maxHeight: 600,
        overflowY: 'auto',
      }}
    >
      <div className={styles.container}>
        <div className={styles.header}>
          <div className={styles.title} title={title}>{title}</div>
          <div className={styles.extra}>
            <Space>
              <span>创建人：{createName}</span>
              <span>公告时间：{publishDate}</span>
            </Space>
          </div>
        </div>
        <div>{projectName}</div>
        <div className={styles.body}>
          <div className={styles.carousel}>
            <Carousel autoplay>
              {photos?.length > 0 ? (
                photos?.map((item) => <img key={item.fileName} src={item?.url} width={300} height={200} />)
              ) : (
                <Empty description="暂无图片" />
              )}
            </Carousel>
          </div>
          <div className={styles.content}>{content}</div>
        </div>
        <div className={styles.attachments}>
          <span>附件：</span>
          <FileListView list={files || []} />
        </div>
      </div>
    </Modal>
  );
};

export default DetailModal;

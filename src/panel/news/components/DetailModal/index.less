.container {
    width: 100%;
    display: flex;
    flex-direction: column;
    font-weight: 400;
    font-size: 14px;
    color: #00000073;
  }
  
  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .title {
      font-weight: 500;
      font-size: 22px;
      color: #000000d9;
      flex: 1;
      overflow: hidden; //超出的文本隐藏
      text-overflow: ellipsis; //溢出用省略号显示
      white-space: nowrap; //溢出不换行
    }
    .extra {
      width: 280px;
      font-weight: 400;
      font-size: 14px;
      color: #00000073;
      text-align: right;
    }
  }
  
  .body {
    margin-top: 16px;
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    font-weight: 400;
    font-size: 14px;
    color: #000000d9;
    line-height: 30px;
    .carousel {
      width: 400px;
      height: 200px;
    }
    .content {
      margin-top: 16px;
      text-indent: 2em;
      white-space: pre-wrap;
    }
  }
  
  .attachments {
    margin-top: 16px;
    display: flex;
    span {
      font-size: 14px;
      color: #000000d9;
      line-height: 22px;
    }
  }
  
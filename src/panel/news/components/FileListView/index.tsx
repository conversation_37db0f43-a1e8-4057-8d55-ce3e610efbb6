// 文件列表展示
import React from 'react';
import { Popover, Space } from 'antd';
import styles from './style.less';

const imgTypeList = ['image/jpeg', 'image/png', 'image/svg+xml', 'image/svg'];

export function download(url, name) {
  fetch(url)
    .then((re) => re.blob())
    .then((re) => {
      const _url = URL.createObjectURL(re);
      const a = document.createElement('a');
      a.href = _url;
      a.download = name;
      a.click();
    });
}

export default function FileListView({
  list,
  viewType = 'list',
}: {
  list: any[];
  viewType?: 'collapse' | 'list';
}) {
  const fileList = list?.filter?.((item) => item && !imgTypeList.includes(item.fileMime)) ?? [];

  if (!list?.length) {
    return <>暂无</>;
  }
  const content = (
    <Space direction="vertical" style={{ width: '90%' }}>
      {fileList?.map((item) => (
        <div key={item.uuid}>
          <div className={styles.row}>
            <a
              onClick={() => download(item.url, item.fileName)}
              className={styles.name}
              title={item.fileName}
            >
              {item.fileName}
            </a>
          </div>
        </div>
      ))}
    </Space>
  );

  if (viewType === 'collapse') {
    return (
      <Popover title="文件预览" content={<div style={{ width: 300 }}>{content}</div>}>
        <a>查看</a>
      </Popover>
    );
  }
  return content;
}

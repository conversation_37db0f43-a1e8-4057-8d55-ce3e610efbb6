import { Empty, Layout, Table } from 'antd';
import React, { useState, useMemo } from 'react';
import { axis, grid } from '@/panel/echartOptions';
import { useRequest } from 'ahooks';
import More from '@/components/more/index';
import ReactEcharts from 'echarts-for-react';
import { getDataZoom } from './slider';
import { getYhfxZgjsl, getYhzgqk } from '@/services/risk';
import { getCurrentUser } from '@/services/api';

const { Content } = Layout;
const HazardCorrection = () => {
  const [dataSource, setDataSource] = useState([]);
  const currentUser = getCurrentUser();

  const { data } = useRequest(async () => {
    const { coId, currentDepartmentId } = currentUser;
    const res: any = currentDepartmentId
      ? await getYhzgqk({ companyId: coId, departmentId: currentDepartmentId })
      : await getYhfxZgjsl({ companyId: coId });
    setDataSource(res.data);
  });

  const columns = [
    {
      title: '项目',
      dataIndex: 'project_name',
      ellipsis: true,
    },
    {
      title: '整改完成率%',
      dataIndex: 'complt_rate',
      ellipsis: true,
      width: 130,
    },
    {
      title: '整改及时率%',
      dataIndex: 'ontime_complt_rate',
      ellipsis: true,
      width: 130,
    },
  ];

  const config = useMemo(() => {
    return {
      grid,
      tooltip: {
        trigger: 'axis',
        formatter: function (params) {
          var relVal = params[0].name;
          for (var i = 0; i < params.length; i++) {
            relVal +=
              '<br/>' + params[i].marker + params[i].seriesName + ' : ' + params[i].value + '%';
          }
          return relVal;
        },
      },
      legend: {
        data: ['整改及时率'],
        itemWidth: 10,
        itemHeight: 10,
      },
      xAxis: {
        type: 'category',
        show: true,
        interval: 0,
        ...axis,
        data: dataSource?.map((x) => x.department_name),
      },
      yAxis: {
        type: 'value',
        name: '%',
        ...axis,
        nameTextStyle: {
          color: '#666666',
        },
      },
      series: [
        {
          name: '整改及时率',
          data: dataSource?.map((x) => x.ontime_complt_rate),
          type: 'bar',
          barMinWidth: 14,
          barMaxWidth: 14,
          color: '#2FC0F9',
        },
      ],
      dataZoom: getDataZoom(
        {
          sliderSize: 8,
          isSliderFromEnd: false,
        },
        dataSource.length,
      ),
    };
  }, [dataSource]);

  function jumpPage(url: string) {
    localStorage.setItem('checkItem', '1');
    window.location.replace(url);
  }

  return (
    <Layout className="layoutPanelPm" style={{ overflow: 'auto' }}>
      <Content style={{ display: 'flex' }}>
        {/* <More jumpUrl="/console/inspect/#/safety" onClick={jumpPage} /> */}
        {currentUser.currentDepartmentId ? (
          <Table
            rowClassName={(_, index) =>
              index % 2 === 0 ? 'homepanel-table-odd-row' : 'homepanel-table-even-row'
            }
            size="small"
            rowKey={(record: any) => `${record.department_name}-${record.project_name}`}
            dataSource={dataSource}
            columns={columns}
            pagination={false}
          />
        ) : dataSource?.length ? (
          <ReactEcharts style={{ width: '100%', height: 300 }} option={config} />
        ) : (
          <div
            style={{
              width: '100%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <Empty />
          </div>
        )}
      </Content>
    </Layout>
  );
};

export default HazardCorrection;

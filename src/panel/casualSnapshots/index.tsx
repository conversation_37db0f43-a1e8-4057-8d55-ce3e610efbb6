// 新闻中心
import More from '@/components/more';
import { useAntdTable, useSize } from 'ahooks';
import { Layout, Table } from 'antd';
import React, { useRef } from 'react';
const { request, getCurrentUser } = window.PMHOME;
const currentUser = getCurrentUser();

export default function CasualSnapshots() {
  const { tableProps } = useAntdTable(({ current, pageSize }) =>
    request(`/api-gateway/casual-snapshots/api/casual/statistics/personnel-ranking`, {
      headers: {
        authorization: localStorage.getItem('accessToken'),
        'site3-c-org': `coId=${currentUser.coId || null};dpId=${
          currentUser.departmentId || null
        };pjId=${currentUser.pjId || null}`,
        'site3-obs-org': `coId=${currentUser.coId || null};dpId=${
          currentUser.departmentId || null
        };pjId=${currentUser.pjId || null}`,
      },
      params: {
        size: pageSize,
        current,
      },
    }).then((re) => {
      return {
        list: re?.data?.records ?? re?.records,
        total: re?.data?.total ?? re?.total,
      };
    }),
  );

  const boxRef = useRef(null);
  const size = useSize(boxRef);

  return (
    <Layout className="layoutPanelPm">
      <Layout.Content>
        <More
          content="更多"
          onClick={() => {
            window.location.href = 'https://aq.cctc.cn/console/casual-snapshots/statistics';
          }}
        />
        <div style={{ height: '100%' }} ref={boxRef}>
          <Table
            size="small"
            scroll={{ y: (size?.height || 200) - 80 }}
            {...tableProps}
            columns={[
              {
                title: '排名',
                render(_, a, index) {
                  return index + 1 + (tableProps?.pagination?.current - 1) * 10;
                },
              },
              {
                title: '创建人',
                dataIndex: 'createMemberName',
              },
              {
                title: '隐患数量',
                dataIndex: 'hiddenNumber',
              },
              {
                title: '随手拍数量',
                dataIndex: 'snapshotsNumber',
              },
            ]}
          />
        </div>
      </Layout.Content>
    </Layout>
  );
}

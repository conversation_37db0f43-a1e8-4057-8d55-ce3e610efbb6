import React, { useEffect, useState } from 'react'
import { Layout, Select } from 'antd'
import More from '@/components/more';
import IndexLess from './index.less';
import { getProjectDeviceList } from '@/services/markRoom';
import { DEVICESTATUS } from './constant';
import Gauge<PERSON>hart from './gaugeChart';

const { Option } = Select;
const { Content } = Layout;

interface IOption {
    value: number;
    status: number;
    label: string;
}

const INITOPTION = {
    value: 0,
    status: 0,
    label: '',
};

export default () => {
    const [optionList, setOptionList] = useState([]);
    const [searchLocation, setsearchLocation] = useState<IOption>(INITOPTION);


    useEffect(() => {
        fetchOptsList();
    }, []);

    // 获取设备信息
    const fetchOptsList = async () => {
        setOptionList([]);
        setsearchLocation(INITOPTION);
        const res = await getProjectDeviceList();
        if (res?.success && res?.data) {
            const { data } = res;
            if (data[0]) {
                // 设备下拉列表默认值
                const { deviceId, location, status } = data[0];
                setsearchLocation({
                    value: deviceId,
                    label: `${location}(${DEVICESTATUS[status]})`,
                    status,
                });
            }
            setOptionList(data || []);
        }
    };

    const realTimeSearch = () => {
        return (
            <Select
                style={{ width: 170 }}
                value={searchLocation}
                labelInValue
                onChange={(_, opt) => {
                    setsearchLocation(opt);
                }}
            >
                {optionList.map((item: { deviceId: number; location: string; status: number }) => (
                    <Option value={item.deviceId} key={item.deviceId} status={item.status}>
                        {`${item.location}(${DEVICESTATUS[item.status]})`}
                    </Option>
                ))}
            </Select>
        );
    };

    return (
        <Layout className={`layoutPanelPm layoutPanelHeight ${IndexLess.layoutPanelHeight}`}>
            <div style={{ position: 'absolute', right: 80, top: 13 }}>
                {realTimeSearch()}
            </div>
            <More jumpUrl='/console/gddnMarkRoom/control#/gddnMarkRoomFront/project' />
            <Content>
                <GaugeChart deviceId={searchLocation.value} status={searchLocation.status} />
            </Content>
        </Layout>
    )
}

import React from 'react';
import ReactECharts from 'echarts-for-react';

interface ChartsProps {
  option?: any;
  width?: number | string;
  height?: number | string;
}
const Chart: React.FC<ChartsProps> = (props) => {
  const { option, width = '100%', height = 300 } = props;

  const getOption = () => {
    return {
      title: {
        text: '',
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: '#6a7985',
          },
        },
      },
      series: [
        {
          name: 'demo',
          type: 'bar',
          data: [5, 20, 36, 10, 10, 20],
        },
      ],
      ...option,
    };
  };

  return <ReactECharts option={getOption()} style={{ width, height }} />;
};

export default Chart;

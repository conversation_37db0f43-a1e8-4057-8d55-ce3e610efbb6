import React, { useEffect, useState } from 'react';
import { getDeviceLiveData } from '@/services/markRoom';
import { INITDATA } from './constant';
import styles from './index.less';
import Chart from './Chart';

interface IProps {
  deviceId: number | null;
  status: number;
}

interface IChartsProps {
  name: string;
  value: number;
  min: number;
  max: number;
  subtext: string;
}

const GaugeChart: React.FC<IProps> = (props) => {
  const { deviceId, status = 0 } = props;
  const [humData, setHumData] = useState(INITDATA);
  const [temData, setTemData] = useState({ ...INITDATA, name: '温度（℃）' });

  const fetchData = async () => {
    const res = await getDeviceLiveData(deviceId);
    if (res?.data) {
      const {
        temp,
        minHumidThreshold,
        minTempThreshold,
        humid,
        maxTempThreshold,
        maxHumidThreshold,
      } = res.data;
      setHumData({
        value: humid,
        min: minHumidThreshold,
        max: maxHumidThreshold,
        name: '湿度（%）',
        subtext: '%',
      });
      setTemData({
        value: temp,
        min: minTempThreshold,
        max: maxTempThreshold,
        name: '温度（℃）',
        subtext: '℃',
      });
    }
  };

  useEffect(() => {
    deviceId && fetchData();
  }, [deviceId]);

  const formatOption = (obj: IChartsProps) => {
    const { value = 0, min = 0, max = 10, name, subtext } = obj;
    const chartMax = name.includes('温') ? 50 : 100;

    // 没有阈值时 显示绿色
    const GAUGECOLOR: Array<[number, string]> =
      min && max
        ? [
          [min / chartMax, '#FA5656'],
          [max / chartMax, '#30D294'],
          [1, '#FA5656'],
        ]
        : [
          [0.5, '#30D294'],
          [1, '#30D294'],
        ];

    return {
      tooltip: {
        transitionDuration: 0,
      },
      grid: {
        top: 0,
        bottom: 0,
        left: 0,
        right: 0,
      },
      series: [
        {
          type: 'gauge',
          startAngle: 210,
          endAngle: -30,
          min: 0,
          max: chartMax,
          detail: {
            // 仪表盘中间文字
            formatter: `{value}`,
            color: status === 0 ? '#c7c7c7' : 'auto',
            fontSize: 24,
          },
          radius: '100%',
          title: {
            fontSize: 14,
            color: '#303133',
            offsetCenter: [0, '-20%'],
          },
          pointer: {
            itemStyle: {
              color: status === 0 ? '#c7c7c7' : 'auto',
            },
            width: 3,
          },
          axisLabel: {
            // 仪表盘刻度
            color: 'auto',
            distance: 2,
            fontSize: 10,
          },
          axisTick: {
            distance: -6,
            length: 4,
            lineStyle: {
              color: '#fff',
              width: 1,
            },
          },
          splitLine: {
            distance: -10,
            length: 30,
            lineStyle: {
              color: '#fff',
              width: 1,
            },
          },
          data: [
            {
              value: value || 0,
              name,
            },
          ],
          axisLine: {
            show: true,
            lineStyle: {
              width: 13,
              shadowBlur: 0,
              color: GAUGECOLOR,
            },
          },
        },
      ],
    };
  };

  return (
    <section className={styles.gauge_wrap}>
      <Chart option={formatOption(temData)} width={200} height={165} />
      <Chart option={formatOption(humData)} width={200} height={165} />
    </section>
  );
};

export default GaugeChart;

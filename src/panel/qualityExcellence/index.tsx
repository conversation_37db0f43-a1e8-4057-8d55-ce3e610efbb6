import More from '@/components/more';
import { getCurrentUser } from '@/services/api';
import { Carousel } from 'antd';
import React, { useEffect, useMemo, useState } from 'react';
import styles from './index.less';

// assets
import { getAwardKanbanListApi } from './assets/api';

export default (props: any) => {
  const currentUser = getCurrentUser();
  const [dataList, setDataList] = useState<any[]>([]);

  const _dataList = useMemo<any[]>(() => {
    const len = dataList.length;
    const res = [];
    const _width = props?.location?.width || 5;
    for (let i = 0; i < Math.ceil(len / _width); i++) {
      res.push(dataList.slice(i * _width, (i + 1) * _width));
    }
    return res;
  }, [props?.location?.width, dataList]);

  // 获取列表
  const getAwardKanbanList = () => {
    getAwardKanbanListApi({ projectId: currentUser.pjId }).then((res) => {
      const { success, result = [] } = res || {};
      if (success) {
        setDataList(result);
      }
    });
  };
  useEffect(() => {
    if (currentUser?.pjId) {
      getAwardKanbanList();
    }
  }, []);

  return (
    <div>
      <More content="详情" jumpUrl="/console/quality-goal/" />
      <section className={styles.content}>
        <Carousel autoplay={true}>
          {_dataList.map((el, index) => (
            <section key={index}>
              <section className={styles.wrap}>
                {el.map((item: any) => (
                  <section key={item.contractAwardId} className={styles.item}>
                    <img
                      src={require(`./assets/images/award_${
                        item?.contractStatus === 2 ? 'a' : 'h'
                      }.png`)}
                      alt=""
                    />
                    <section className={styles.title}>{item.awardName}</section>
                  </section>
                ))}
              </section>
            </section>
          ))}
        </Carousel>
      </section>
    </div>
  );
};

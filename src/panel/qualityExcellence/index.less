.content {
  .wrap {
    display: flex;
    align-items: center;
    height: 300px;

    .item {
      display: inherit;
      flex: 1;
      flex-direction: column;
      align-items: center;
      .title {
        width: 132px;
        height: 36px;
        line-height: 36px;
        text-align: center;

        background-image: radial-gradient(#ffe190, #fca42f);
      }
    }
  }

  :global {
    .ant-carousel .slick-dots li button {
      background: #909399 !important;
    }
  }
}

// import More from '@/components/more'
import { Col, Layout, Row } from 'antd'
import React from 'react'
import Styles from './index.less'
const { Content } = Layout;
export default () => {
  return (
    <div className={Styles.projectNotice}>
      <Content className={Styles.mainCon}>
        <div className={Styles.more} style={{ width: '50px', position: 'absolute', right: '0', top: '19px', cursor: 'pointer', color: '#0b0eff' }} >更多</div>
        <ul className={Styles.ulCont}>
          <li className={Styles.liList}>
            <div className={Styles.lists}>
              <div className={Styles.listsName}>Bim中心机电深化协调会</div>
              <div className={Styles.listsTime}>2020-4-13</div>
            </div>
          </li>
          <li className={Styles.liList}>
            <div className={Styles.lists}>
              <div className={Styles.listsName}>质监站安全检查通知</div>
              <div className={Styles.listsTime}>2022-4-27</div>
            </div>
          </li>
          <li className={Styles.liList}>
            <div className={Styles.lists}>
              <div className={Styles.listsName}>大型设备专项方案论证通知</div>
              <div className={Styles.listsTime}>2022-4-29</div>
            </div>
          </li>
        </ul>
      </Content>
    </div>
  )
}

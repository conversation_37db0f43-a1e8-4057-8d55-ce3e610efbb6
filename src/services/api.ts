export interface IPanel {
  funCode: string;
  panelName: string;
  panelNo: string;
  panelId: number;
  system?: boolean;
  location?: any;
  Component?: any;
}
export interface IList {
    list: IPanel[]
}

export const getCompanyId = () => {
  const {getCurrentUser} = window.PMHOME;
  const currentUser = getCurrentUser();
  return currentUser.type === 1 ? {companyId: currentUser.coId} : {projectId: currentUser.pjId};
}

export const getCurrentUser = () => {
  const {getCurrentUser} = window.PMHOME;
  return getCurrentUser();
}

export function findPanelList() {
  const {request, server} = window.PMHOME;
  const data = getCompanyId();
  return request(`${server.homeServer}/front/panel/list.htm`, {
    method: 'POST',
    requestType: 'form',
    data,
  })
}

interface ISavePanel{
  panelId: number | string;
  location: string;
}

interface DiscussObj {
  sourceId: string,
  status: number
}

export function savePanelList(data: ISavePanel[], actionRange: number) {
  const {request, server,} = window.PMHOME;
  return request(`${server.homeServer}/front/panel/save.htm`, {
    method: 'POST',
    requestType: 'form',
    data: {panel: JSON.stringify(data), ...getCompanyId(), actionRange},
  })
}
export function findUserList() {
  const {request, server} = window.PMHOME;
  const data = getCompanyId();
  let url = `${server.homeServer}/front/panel/userList.htm`
  if (data.projectId) {
    url = `${server.homeServer}/front/panel/userList.htm?projectId=${data.projectId}`
  }
  return request(url, {
    method: 'GET',
    requestType: 'form',
  })
}
export function isPanelManger() {
  const {request, server} = window.PMHOME;
  const data = getCompanyId();
  let url = `${server.homeServer}/front/panel/isManger.htm`
  if (data.projectId) {
    url = `${server.homeServer}/front/panel/isManger.htm?projectId=${data.projectId}`
  }
  return request(url, {
    method: 'GET',
    requestType: 'form',
  })
}
export function revertDefault(perOrCom?: number) {
  const {request, server} = window.PMHOME;
  const data = getCompanyId();
  let actionRange = null
  if (perOrCom) {
    actionRange = perOrCom
  }
  let url = `${server.homeServer}/front/panel/revertDefault.htm?actionRange=${actionRange}`
  if (data.projectId) {
    url = `${server.homeServer}/front/panel/revertDefault.htm?projectId=${data.projectId}&actionRange=${actionRange}`
  }
  return request(url, {
    method: 'GET',
    requestType: 'form',
    data,
  })
}

export function disposeJoinDiscuss(data: DiscussObj) {
  const {request, server} = window.PMHOME;
  return request(`${server.oaServer}/front/disposeJoinDiscuss.htm`, {
    method: 'POST',
    requestType: 'form',
    data: {
      'joinDiscuss.join_id': data.sourceId,
      'joinDiscuss.status': data.status
    }
  })
}

export function getProjectDistribution() {
  const {request, server} = window.PMHOME;
  return request(`${server.schedulePlanServer}/companyStatistics/getProjectDistribution`, {
    method: 'POST'
  })
}

export function getTaskWarn() {
  const {request, server, getCurrentUser} = window.PMHOME;
  const currentUser = getCurrentUser();
  return request(`${server.schedulePlanServer}/statistics/getFirstTaskWarnStatisticByCoId/${currentUser.coId}`, {
    method: 'GET'
  })
}

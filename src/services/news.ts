interface NewsId {
  id: number;
}

interface NoticeInfo {
  currentPage: number;
  pageSize: number;
  isMessageCenter: number;
}

const getCompanyId = () => {
  const { getCurrentUser } = window.PMHOME;
  const currentUser = getCurrentUser();
  return currentUser.type === 1 ? { companyId: currentUser.coId } : { projectId: currentUser.pjId };
};

export function getNews() {
  const { request, server } = window.PMHOME;
  const data: any = {};
  // let url = `${server.homeServer}/front/news/list.htm`
  let url = `${server.homeServer}/front/banner/findOwnBannerList.htm`;
  const { getCurrentUser } = window.PMHOME;
  const currentUser = getCurrentUser();
  // 根据id判断传值类型 项目/子公司/公司
  if (currentUser.pjId) {
    data['query.project_id'] = currentUser.pjId;
  } else if (currentUser.currentDepartmentId) {
    data['query.department_id'] = currentUser.currentDepartmentId;
  } else {
    data['query.company_id'] = currentUser.coId;
  }
  return request(url, {
    method: 'POST',
    requestType: 'form',
    data: {
      ...data,
    },
  }).then((res: any) => {
    const ret = { list: [] };
    if (!res.bannerList || !res.bannerList.dataList) return ret;
    const list = res.bannerList.dataList || [];
    return { list };
  });
}

export function getNewsInfo(data: NewsId) {
  const { request, server } = window.PMHOME;
  let url = `${server.homeServer}/front/banner/find.htm`;
  return request(url, {
    method: 'POST',
    requestType: 'form',
    data: {
      'query.id': data.id,
    },
  }).then((res: any) => {
    const news = { ...res.banner };
    return { news };
  });
}

export function noticeList(data: NoticeInfo) {
  const { request, server } = window.PMHOME;
  let url = `${server.oaServer}/front/notice/noticeList.htm`;
  return request(url, {
    method: 'POST',
    requestType: 'form',
    data: {
      'query.currentPage': data.currentPage,
      'query.pageSize': data.pageSize,
      'query.is_message_center': data.isMessageCenter,
    },
  });
}

import { getCurrentUser } from './api';
import server from '@/services/service'

export function fetchVideoCompanyMonitor() {
    const { request,} = window.PMHOME;
    const currentUser = getCurrentUser();
    return request(`${server.videoCenterServer}/onlineTrend/company/get`, {
        method: 'POST',
        data: {
            mid: currentUser.mid,
            companyId: currentUser.coId
        },
        closeThrowErr: true
    })
}

export function fetchVideoProjectMonitor() {
    const { request, } = window.PMHOME;
    const currentUser = getCurrentUser();
    return request(`${server.videoCenterServer}/onlineTrend/project/get`, {
        method: 'POST',
        data: {
            mid: currentUser.mid,
            projectId: currentUser.pjId
        },
        closeThrowErr: true
    })
}

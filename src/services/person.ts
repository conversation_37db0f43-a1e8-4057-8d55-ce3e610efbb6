import { getCurrentUser } from './api';
import service from "@/services/service";

export function getWeekAttendance() {
    const { request, } = window.PMHOME;
    const currentUser = getCurrentUser();
    return request(`${service.corPerson}/person/attendance/getWeekAttendance`, {
        method: 'POST',
        data: {
          type: currentUser.type,
          projectId: currentUser.pjId,
          companyId: currentUser.coId
        },
        closeThrowErr: true
    })
}
export function getHomeCount() {
    const { request, } = window.PMHOME;
    const currentUser = getCurrentUser();
    return request(`${service.corPerson}/person/attendance/getHomeCount`, {
        method: 'POST',
        data: {
          type: currentUser.type,
          projectId: currentUser.pjId,
          companyId: currentUser.coId
        },
        closeThrowErr: true
    })
}
export function getCompanyAttendanceRank(orderBy: number) {
    const {request,} = window.PMHOME;
    const currentUser = getCurrentUser();
    return request(`${service.corPerson}/person/attendance/getCompanyAttendanceRank`, {
        method: 'POST',
        data: {
          companyId: currentUser.coId,
          orderBy
        },
        closeThrowErr: true
    })
}
export function getWeekAttendanceAvg(orderBy: number) {
    const {request,} = window.PMHOME;
    const currentUser = getCurrentUser();
    return request(`${service.corPerson}/person/attendance/getWeekAttendanceAvg`, {
        method: 'POST',
        data: {
          companyId: currentUser.coId,
          orderBy
        },
        closeThrowErr: true
    })
}
export function getSceneWorkerCountByProfession(orderBy: number) {
    const {request,} = window.PMHOME;
    const currentUser = getCurrentUser();
    return request(`${service.corPerson}/person/attendance/getSceneWorkerCountByProfession`, {
        method: 'POST',
        data: {
          companyId: currentUser.coId,
          projectId: currentUser.pjId,
          orderBy
        },
        closeThrowErr: true
    })
}
export function getSceneWorkerCountByCooperator(orderBy: number) {
    const {request,} = window.PMHOME;
    const currentUser = getCurrentUser();
    return request(`${service.corPerson}/person/attendance/getSceneWorkerCountByCooperator`, {
        method: 'POST',
        data: {
          companyId: currentUser.coId,
          projectId: currentUser.pjId,
          orderBy
        },
        closeThrowErr: true
    })
}
// 出入考勤统计 (新)
export function getAttendanceHomepageOverview() {
    const {request} = window.PMHOME;
    const currentUser = getCurrentUser();
    return request(`${service.zhgdPerson}/attendance/homepage/overview`, {
        method: 'POST',
        data: {
          companyId: currentUser.coId,
          projectId: currentUser.pjId,
        },
    })
}
// 近7日出勤趋势 (新)
export function getAttendanceHomepageTrend(dataObj: { startDate: string; endDate: string; }) {
  const { request, } = window.PMHOME;
  const currentUser = getCurrentUser();
  return request(`${service.zhgdPerson}/attendance/homepage/trend`, {
      method: 'POST',
      data: {
        ...dataObj,
        projectId: currentUser.pjId,
        companyId: currentUser.coId,
      },
  })
}
// 今日现场人数排行 (新)
export function getAttendanceWorkerRank(dataObj: any) {
  const { request, } = window.PMHOME;
  const currentUser = getCurrentUser();
  return request(`${service.zhgdPerson}/attendance/homepage/scene/worker/rank`, {
      method: 'POST',
      data: {
        ...dataObj,
        projectId: currentUser.pjId,
        companyId: currentUser.coId,
      },
  })
}
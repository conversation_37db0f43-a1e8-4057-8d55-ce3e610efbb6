/*
 * @Author: wendy
 * @Date: 2020-08-26 09:45:49
 * @LastEditors: wendy
 * @LastEditTime: 2020-08-27 09:38:58
 * @Description:
 */
import server from '@pms/pmserver';
export default {
  ...server,
  gddnTowerServer: '/gddnTower', // 塔吊
  gddnElevatorServer: '/gddnElevator', // 升降机
  gddnDustServer: '/gddnDust', // 环境
  videoCenterServer: '/videoCenter/api', // 视频中心
  companyServer: '/company/api',
  schedulePlanServer: '/schedule-plan/api',
  corPerson: '/corPerson/api',
  agilebpmServer: '/agilebpm/api',
  taskcenterServer: '/taskcenter/api',
  dataBoardServer: '/dataBoard/api',
  authorityServer: server.companyServer,
  plugServe: server.companyPlugServer,
  platformConfigServer: '/platformConfig/api',
  zhgdPerson: '/zhgd-person/api',
  epidemicServer: '/epidemic/api', //工地防疫
  roomMarkServer: '/gddnMarkRoom/api', //标养室
  // workPlanServer:'/mechatronics', // 工作计划
  // approveServer:'/bim' // 审批
  productionManageServer: '/schedule-management/api',
};

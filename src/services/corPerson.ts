import { getCurrentUser } from '@/services/api';
import moment from 'moment';

const {request} = window.PMHOME;
const user = getCurrentUser();

// ----------------------------- 出勤统计 --------------------------------
export async function updateStatistic () {
  return request.post(`/corPerson/api/attendance/statistics/updateAttendance/${getCurrentUser().coId}`)
}

type YYYYMMDD = string;
// 企业/子公司 统计
interface IStatistic {
  companyId?: any;
  departmentId?: any;
  projectStatus?: any; // 1-进行中(在建),2-完成(完工),5-筹备,6-立项,7-停工(这里传1)
  recordDate?: YYYYMMDD;
  complianceRate?: any; // 达标率
}
export async function coStatistic (data: IStatistic) {
  return request.post(`/corPerson/api/attendance/statistics/getAttendanceStatistics`, {
    data,
  })
}

// 企业级列表
export async function coRankList (data: Omit<IStatistic, 'departmentId'>) {
  return request.post('/corPerson/api/attendance/statistics/findAttendanceRankByCompany', {
    data
  })
}

// 子公司级列表
export async function departmentRankList (data: IStatistic & {
  page: number;
  pageSize: number;
}) {
  return request.post('/corPerson/api/attendance/statistics/findAttendanceRankByDept', {
    data: {
      ...data,
      "orderByKey": "avgAttendanceRate",
    }
  })
}

// 项目级 - 统计
export async function pjStatistic () {
  return request.post(`/zhgd-person/api/attendance/homepage/overview`, {
    data: {
      companyId: user.coId,
      projectId: user.pjId
    }
  })
}

// 项目级 - 统计
export async function pjLatest30DaysTrends () {
  return oldPjLatest30DaysTrends().then(res => res?.result?.map((x: any) => {
    return {
      attendanceCount: x?.attendanceCnt || 0,
      attendanceRate: x?.attendanceRate || 0,
      presentCount: x?.presenceCnt || 0,
      recordDate: x?.recordDate
    }
  }) || [])

  // return request.post(`/corPerson/api/attendance/statistics/findMonthAttendanceTrend`, {
  //   data: {
  //     companyId: user.coId,
  //     complianceRate: 60,
  //     departmentId: user.currentDepartmentId,
  //     projectId: user.pjId,
  //     projectStatus: 1,
  //     recordDate: moment().format('YYYY-MM-DD')
  //   }
  // })
}

// 项目级 - 统计（旧的接口）
async function oldPjLatest30DaysTrends () {
  const now = moment();
  return request.post(`/zhgd-person/api/attendance/analyse/trend`, {
    data: {
      personType: null,
      projectId: user.pjId,
      companyId: user.coId,
      endDate: now.format('YYYY-MM-DD'),
      startDate: now.subtract(1, 'month').format('YYYY-MM-DD'),
    }
  })
}


/*
 * @Author: wendy
 * @Date: 2020-08-26 16:52:49
 * @LastEditors: wendy
 * @LastEditTime: 2020-08-27 09:05:45
 * @Description: 进度相关api
 */
import Server from './service';

export const getCompanyIdAndProjectId = () => {
  const {getCurrentUser} = window.PMHOME;
  const currentUser = getCurrentUser();
  return {companyId: currentUser.coId, projectId: currentUser.pjId};
}

export const isCompany = () => {
  const {getCurrentUser} = window.PMHOME;
  const currentUser = getCurrentUser();
  return currentUser.type === 1;
}

// 一级节点受控率
export function fetchProjectNodeOneRate() {
  const {request} = window.PMHOME;
  const {companyId, projectId} = getCompanyIdAndProjectId();
  return request(`${Server.schedulePlanServer}/schedulePlan/queryProjectNodeOneRate/${companyId}/${projectId}`, {
    method: 'GET',
    requestType: 'form',
  })
}

// 二级节点受控率
export function fetchProjectNodeTwoRate() {
  const {request} = window.PMHOME;
  const {companyId, projectId} = getCompanyIdAndProjectId();
  return request(`${Server.schedulePlanServer}/schedulePlan/queryProjectNodeTwoRate/${companyId}/${projectId}`, {
    method: 'GET',
    requestType: 'form',
  })
}

// 项目进度预警等级分布
export function fetchTaskWarnStatistic() {
  const {request} = window.PMHOME;
  const {companyId, projectId} = getCompanyIdAndProjectId();
  return request(`${Server.schedulePlanServer}/statistics/getFirstTaskWarnStatisticByPjId/${projectId}`, {
    method: 'GET',
    requestType: 'form',
  })
}

// 项目进度预警任务列表
export function fetchWarnTaskList() {
  const {request} = window.PMHOME;
  const {companyId, projectId} = getCompanyIdAndProjectId();
  return request(`${Server.schedulePlanServer}/schedulePlan/queryWarnTaskList/${companyId}/${projectId}`, {
    method: 'GET',
    requestType: 'form',
  })
}

// 项目进度接口
export function getSchedulePlanDeadline() {
  const { request } = window.PMHOME;
  const { projectId } = getCompanyIdAndProjectId();
  return request(`${Server.schedulePlanServer}/statistics/getSchedulePlanDeadline/${projectId}`, {
    method: 'GET',
    requestType: 'form',
  });
}

interface IRank {
  op1: number;
  op2: number;
}
// 企业层 进度 排行
export function getScheduleRank(pamar: IRank) {
  const {request} = window.PMHOME;
  return request(`${Server.schedulePlanServer}/companyStatistics/getScheduleRank`, {
    method: 'POST',
    data: pamar
  })
}
interface IPage {
  pageSize: number;
  page: number;
}
/**
 *  企业层 -  首页 滞后节点排行
 */
export async function getScheduleRankDelay(params: IPage) {
  const {request} = window.PMHOME;
  return request(`${Server.schedulePlanServer}/companyStatistics/getScheduleRankDelay`,{method: 'POST', data: params})}

interface INode extends IPage{
  nodeLevel: number;
}
/**
 *  企业层 -  首页 一二级 节点受控率
 */
export async function getScheduleRankNodeControl(params: INode) {
  const {request} = window.PMHOME;
  return request(`${Server.schedulePlanServer}/companyStatistics/getScheduleRankNodeControl`,{method: 'POST', data: params})}

// 企业层 进度 项目分布
export function getProjectDistribution() {
  const {request} = window.PMHOME;
  return request(`${Server.schedulePlanServer}/companyStatistics/getProjectDistribution`, {
    method: 'POST'
  })
}
// 企业层 进度 受控率
export function getTaskWarn() {
  const {request} = window.PMHOME;
  const {companyId} = getCompanyIdAndProjectId();
  return request(`${Server.schedulePlanServer}/statistics/getFirstTaskWarnStatisticByCoId/${companyId}`, {
    method: 'GET'
  })
}

import server from './service';
import { getCurrentUser } from './api';

const {dataBoardServer,authorityServer,plugServe} = server;

export async function getStandardList() {
  const { request, } = window.PMHOME;
  const currentUser = getCurrentUser()
  const data = {
    companyId: currentUser.type === 1 ? currentUser.coId : null,
    dataRange: currentUser.type,
    projectId: currentUser.type === 2 ? currentUser.pjId : null,
  };
  return request(`${dataBoardServer}/zhgd/board/list`, {
    method: 'POST',
    data,
    closeThrowErr: true
  });
}

export async function findUserFunByFunCodes(funCodes: string[]) {debugger
  const { request, } = window.PMHOME;
  return request(`${authorityServer}/front/findUserFunByFunCodes.htm`, {
    method: 'POST',
    requestType: 'form',
    data: {
      'userFunctionQuery.functionCodes': 'c_decision_com_view'
    }
  });
}

export async function menus() {
  const { request, } = window.PMHOME;
  return request.post(`${plugServe}/nav/menus.htm?auto=true`);
}

interface IAuthority {
  projectId?: string;
  companyId?: string;
  plugNo: string;
}
export async function getPlugAuthority(data: IAuthority) {
  const { request, } = window.PMHOME;
  const {plugNo, projectId, companyId} = data;
  let url = `${plugServe}/manage/plug/isAuthority.htm?plugNo=${plugNo}`;
  if (companyId) {
    url = `${url}&companyId=${companyId}`;
  }
  if (projectId) {
    url = `${url}&projectId=${projectId}`;
  }
  return request.get(url);
}

// 获取已配置的大屏和应用列表接口
export async function decisionMakingGetConfig() {
  const { request } = window.PMHOME;
  const currentUser = getCurrentUser()
  const data = {
    companyId: currentUser.coId,
    projectId: currentUser.type === 2 ? currentUser.pjId : null
  };
  return request(`${server.platformConfigServer}/decisionMaking/getConfig`, {
    method: 'POST',
    data
  });
}

export async function getPlugStatusType(data: IAuthority) {
  const { request, } = window.PMHOME;
  const {plugNo, projectId, companyId} = data;
  let url = `${plugServe}/manage/plug/getPlugStatusType.htm?plugNo=${plugNo}`;
  if (companyId) {
    url = `${url}&companyId=${companyId}`;
  }
  if (projectId) {
    url = `${url}&projectId=${projectId}`;
  }
  return request.get(url);
}

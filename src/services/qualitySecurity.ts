/*
 * @Author: wendy
 * @Date: 2020-08-26 16:52:49
 * @LastEditors: wendy
 * @LastEditTime: 2020-08-26 20:25:36
 * @Description: 质量&安全相关api
 */
import {isCompany, getCompanyIdAndProjectId} from './progress';

export function fetchInspectBoard(itemType: number) {
  const {request, server} = window.PMHOME;
  const {projectId} = getCompanyIdAndProjectId();
  return request(`${server.inspectService}/front/inspect/findInspectBoard`, {
    method: 'POST',
    requestType: 'form',
    data: {
      'query.projectId': isCompany() ? null : projectId,
      'query.itemType': itemType
    },
  })
}

import { getCurrentUser } from './api';

// 风险管控情况-柱状图
export function getFxgkqkZzt(params) {
    const { request, } = window.PMHOME;
    return request(`/dataview/api/corp/get?operatorInterCode=aqdp-gsc-fxgkqk-zzt`, {
        method: 'GET',
        params,
        closeThrowErr: true
    })
}

// 风险管控情况-项目列表（企业，分公司）
export function getFxgkqkXmlb(params) {
    const { request, } = window.PMHOME;
    return request(`/dataview/api/corp/get?operatorInterCode=aqdp-gsc-fxgkqk-xmlb`, {
        method: 'GET',
        params,
        closeThrowErr: true
    })
}

// 检查覆盖率排行
export function getJcfglph(params) {
    const { request, } = window.PMHOME;
    return request(`/dataview/api/corp/get?operatorInterCode=aqdp-gsc-jcfglph`, {
        method: 'GET',
        params,
        closeThrowErr: true
    })
}

// 隐患分析-整改及时率
export function getYhfxZgjsl(params) {
    const { request, } = window.PMHOME;
    return request(`/dataview/api/corp/get?operatorInterCode=aqdp-gsc-yhfx-zgjsl`, {
        method: 'GET',
        params,
        closeThrowErr: true
    })
}

// 隐患整改情况-分公司
export function getYhzgqk(params) {
    const { request, } = window.PMHOME;
    return request(`/dataview/api/corp/get?operatorInterCode=aqdp-fgs-yhzgqk`, {
        method: 'GET',
        params,
        closeThrowErr: true
    })
}

// 不同等级隐患整改情况-项目级
export function getBtdjyhzgqk(params) {
    const { request, } = window.PMHOME;
    return request(`/dataview/api/corp/get?operatorInterCode=aqdp-xmc-btdjyhzgqk`, {
        method: 'GET',
        params,
        closeThrowErr: true
    })
}

// 危大工程-方案管控缺失
export function getWdgcFagkqs(params) {
    const { request, } = window.PMHOME;
    return request(`/dataview/api/corp/get?operatorInterCode=aqdp-xmc-wdgc-fagkqs`, {
        method: 'GET',
        params,
        closeThrowErr: true
    })
}

// 项目概况
export function getGgXmxx(params) {
    const { request, } = window.PMHOME;
    return request(`/dataview/api/corp/get?operatorInterCode=gg-xmxx`, {
        method: 'GET',
        params,
        closeThrowErr: true
    })
}
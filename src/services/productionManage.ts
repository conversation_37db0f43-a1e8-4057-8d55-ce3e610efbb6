import service from '@/services/service';
const { request } = window.PMHOME;
const server = service.productionManageServer;
//分公司管控情况-进度受控
export async function deptScheduleControl(data: any) {
  return request(`${service.productionManageServer}/show/company/deptScheduleControl`, {
    method: 'POST',
    data,
  });
}

//分公司管控情况-进度覆盖
export async function deptScheduleCoverage(data: any) {
  return request(`${server}/show/company/deptScheduleCoverage`, {
    method: 'POST',
    data,
  });
}

export async function controlCoverage(data: any) {
  return request(`${server}/show/company/controlCoverage`, {
    method: 'POST',
    data,
  });
}

// 进度受控率
export async function scheduleControl(data: any) {
  return request(`${server}/show/company/scheduleControl`, {
    method: 'POST',
    data,
  });
}

// 查询总进度工期
export async function getOverallPlanDetail(data: any) {
  return request(`${server}/show/project/getOverallPlanDetail`, {
    method: 'POST',
    data,
  });
}

export async function getAuditFinishInfoList(data: any) {
  return request(`${server}/schedule-info/getAuditFinishInfoList`, {
    method: 'POST',
    data,
  });
}

//获取最大风险计划
export async function getOverallMaxWarningInfo(data: any) {
  return request(`${server}/show/project/getOverallMaxWarningInfo`, {
    method: 'POST',
    data,
  });
}

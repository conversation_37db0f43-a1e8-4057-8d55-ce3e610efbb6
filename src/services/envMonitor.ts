import { getCurrentUser } from './api';
import server from "@/services/service";

export interface EnvProjectMonitor {
    deviceId: number;
    deviceType: number;
}

export function fetchEnvCompanyMonitor() {
    const { request, } = window.PMHOME;
    const currentUser = getCurrentUser();
    return request(`${server.gddnDustServer}/api/dust/company/homePage/statis`, {
        method: 'POST',
        data: {
            userId: currentUser.mid,
            companyId: currentUser.coId
        },
        closeThrowErr: true
    })
}

export function fetchEnvProjectMonitor(params: EnvProjectMonitor) {
    const { request, } = window.PMHOME;
    const currentUser = getCurrentUser();
    return request(`${server.gddnDustServer}/api/dust/project/homePage/statis`, {
        method: 'POST',
        data: {
            userId: currentUser.mid,
            projectId: currentUser.pjId,
            ...params
        },
        closeThrowErr: true
    })
}

export function fetchEnvAllDeviceList() {
    const { request, } = window.PMHOME;
    const currentUser = getCurrentUser();
    return request(`${server.gddnDustServer}/api/dust/device/allDeviceList`, {
        method: 'POST',
        data: {
            userId: currentUser.mid,
            projectId: currentUser.pjId
        },
        closeThrowErr: true
    })
}
/**
 * bim 卡片接口
 */

import { getCurrentUser } from './api';

const bimfile = '/bimfile';

/**
 * 企业看板 - 文件上传情况按月统计 - 柱状图
 */
export const companyDocUploadStatsService = () => {
  const { request } = window.PMHOME;
  const currentUser = getCurrentUser();
  return request(`${bimfile}/statistics/companyDocUploadStats`, {
    method: 'POST',
    gateway: true,
    data: {
      companyId: currentUser.coId,
      subCompanyId: currentUser.currentDepartmentId,
    },
  });
};

/**
 * 企业看板 - 文件上传情况按项目统计 - 列表
 */
export const companyDocProjectUploadStatsService = () => {
  const { request } = window.PMHOME;
  const currentUser = getCurrentUser();
  return request(`${bimfile}/statistics/companyDocProjectUploadStats`, {
    method: 'POST',
    gateway: true,
    data: {
      companyId: currentUser.coId,
      subCompanyId: currentUser.currentDepartmentId,
    },
  });
};

/**
 * 项目看板 - 文件上传情况按用户统计 - 横向柱状图
 */
export const userDocTypeStatsStatsService = () => {
  const { request } = window.PMHOME;
  const currentUser = getCurrentUser();
  return request(`${bimfile}/statistics/userDocTypeStats`, {
    method: 'POST',
    gateway: true,
    data: {
      companyId: currentUser.coId,
      subCompanyId: currentUser.currentDepartmentId,
      busId: currentUser.guid,
    },
  });
};

/**
 * 企业/项目看板 - 问题统计
 */
export const problemStsService = (data: any) => {
  const { request } = window.PMHOME;
  const currentUser = getCurrentUser();
  return request(`${bimfile}/statistics/problemSts`, {
    method: 'POST',
    gateway: true,
    data: {
      companyId: currentUser.coId,
      subCompanyId: currentUser.currentDepartmentId,
      busId: currentUser.guid,
      pjIds: currentUser.guid ? [currentUser.guid] : [],
      ...data,
    },
  });
};

/**
 * 问题统计-饼状图
 * @param query
 * @returns
 */
export async function problemStsApi(query: any) {
  const currentUser = getCurrentUser();
  const { request } = window.PMHOME;
  // console.log(currentUser, 'currentUser');
  let data = {
    ...query,
    companyId: currentUser.coId,
    subCompanyId: currentUser.currentDepartmentId ? currentUser.currentDepartmentId : undefined,
    pjIds: currentUser.ccbimProjectId ? [currentUser.ccbimProjectId] : undefined,
    busId: currentUser.ccbimProjectId ? currentUser.ccbimProjectId : undefined,
  };
  return request(`${bimfile}/statistics/problemSts`, {
    method: 'POST',
    data,
    gateway: true,
    // headers: {
    //   'panshi-service-tag': 'xhs',
    //   "_api-gateway-access-data_encode": "%7B%22id%22%3A%222c91808263953f04016395d1df680005%22%2C%22loginName%22%3A%22default%22%2C%22memberName%22%3A%22default%22%2C%22ipAddress%22%3A%220.0.0.0%22%2C%22loginTime%22%3A%222023-01-06T09%3A10%3A30.658%2B00%3A00%22%2C%22appKey%22%3A%228a9ca3205bb50be7015bb50be74a0000%22%2C%22createTime%22%3A%222023-01-06T09%3A10%3A30.658%2B00%3A00%22%2C%22expiredSecond%22%3A2147483647%7D"
    // }
  });
}

/**
 * 企业看板-文件上传情况按类型统计
 * @returns
 */
export async function companyDocTypeUploadStatsApi() {
  const currentUser = getCurrentUser();
  const { request } = window.PMHOME;

  let data = {
    companyId: currentUser.coId,
    subCompanyId: currentUser.currentDepartmentId,
    // pjIds: currentUser.projectId ? [ currentUser.projectId ] : undefined
  };
  return request(`${bimfile}/statistics/companyDocTypeUploadStats`, {
    method: 'POST',
    data,
    gateway: true,
    // headers: {
    //   'panshi-service-tag': 'zht',
    // }
  });
}

/**
 * 项目看板-文件上传情况按类型统计
 * @returns
 */
export async function projectDocTypeUploadStatsApi() {
  const currentUser = getCurrentUser();
  const { request } = window.PMHOME;

  let data = {
    companyId: currentUser.coId,
    busId: currentUser.ccbimProjectId ? currentUser.ccbimProjectId : undefined,
  };
  return request(`${bimfile}/statistics/projectDocTypeUploadStats`, {
    method: 'POST',
    data,
    gateway: true,
    // headers: {
    //   'panshi-service-tag': 'zht',
    // }
  });
}

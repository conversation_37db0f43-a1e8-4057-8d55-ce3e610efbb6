/**
 * bim 卡片接口
 */
import { getCurrentUser } from './api';

const bimfile = '/bimfile';

/**
 * 企业看板 - 文件上传情况按月统计 - 柱状图
 */
export const collaGetProjectsCm = (data: any) => {
  const { request } = window.PMHOME;
  const currentUser = getCurrentUser();
  return request(`${bimfile}/colla/getProjects`, {
    method: 'POST',
    gateway: true,
    data: {
      companyId: currentUser.coId,
      currentPage: data.currentPage,
      pageSize: data.pageSize,
    },
  });
};

export const collaGetMemberTaskListPm = () => {
  const { request } = window.PMHOME;
  const currentUser = getCurrentUser();
  return request(`${bimfile}/colla/getMemberCollaTaskList`, {
    method: 'POST',
    gateway: true,
    data: {
      companyId: currentUser.coId,
      projectId: currentUser.projectId,
    },
  });
};

export const getStatisticList = () => {
  const { request } = window.PMHOME;
  const currentUser = getCurrentUser();
  return request(`${bimfile}/colla/getStatisticList`, {
    method: 'GET',
    gateway: true,
    params: {
      busId: currentUser.projectId,
    },
  });
};

export const getVisitAndDownCount = () => {
  const { request } = window.PMHOME;
  const currentUser = getCurrentUser();
  return request(`${bimfile}/colla/getVisitAndDownCount`, {
    method: 'GET',
    gateway: true,
    params: {
      companyId: currentUser.coId,
    },
  });
};

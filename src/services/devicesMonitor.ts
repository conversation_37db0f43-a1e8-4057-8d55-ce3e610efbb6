import { getCurrentUser } from './api';
import server from '@/services/service.ts';
export function fetchElevatorCompanyMonitor() {
    const { request, } = window.PMHOME;
    const currentUser = getCurrentUser();
    return request(`${server.gddnElevatorServer}/api/elevator/company/online/device/trend`, {
        method: 'POST',
        data: {
            userId: currentUser.mid,
            companyId: currentUser.coId
        },
        closeThrowErr: true
    })
}

export function fetchElevatorProjectMonitor() {
    const { request, } = window.PMHOME;
    const currentUser = getCurrentUser();
    return request(`${server.gddnElevatorServer}/api/elevator/project/online/device/trend`, {
        method: 'POST',
        data: {
            userId: currentUser.mid,
            companyId: currentUser.coId,
            projectId: currentUser.pjId
        },
        closeThrowErr: true
    })
}

export function fetchTowerCompanyMonitor() {
    const { request, } = window.PMHOME;
    const currentUser = getCurrentUser();
    return request(`${server.gddnTowerServer}/api/tower/company/homePage/statis`, {
        method: 'POST',
        data: {
            userId: currentUser.mid,
            companyId: currentUser.coId
        },
        closeThrowErr: true
    })
}

export function fetchTowerProjectMonitor() {
    const { request, } = window.PMHOME;
    const currentUser = getCurrentUser();
    return request(`${server.gddnTowerServer}/api/tower/project/homePage/statis`, {
        method: 'POST',
        data: {
            userId: currentUser.mid,
            companyId: currentUser.coId,
            projectId: currentUser.pjId
        },
        closeThrowErr: true
    })
}

import { getCurrentUser } from './api';
import server from '@/services/service';

// 获取设备列表接口
export async function getProjectDeviceList() {
  const { request } = window.PMHOME;
  const currentUser = getCurrentUser();
  return request(`${server.roomMarkServer}/standCuringRoom/device/getProjectDeviceList?projectId=${currentUser.pjId}`, {
    method: 'GET',
  });
}

// 设备实时数据
export async function getDeviceLiveData(deviceId: number | null) {
  const { request } = window.PMHOME;
  const currentUser = getCurrentUser();
  return request(`${server.roomMarkServer}/standCuringRoom/monitorData/getDeviceLiveData`, {
    method: 'GET',
    params: {
      projectId: currentUser.pjId,
      deviceId,
    },
  });
}

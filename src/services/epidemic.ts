import { getCurrentUser } from './api';
import service from "@/services/service";
import qs from 'qs'
export function getEpidemicDataApi(personType: number | string) { //防疫情况
    const { request, } = window.PMHOME;
    const currentUser = getCurrentUser();
    return request(`${service.epidemicServer}/epidemic/getEpidemicCondition`, {
        method: 'POST',
        data: {
            personType,
            projectId: currentUser.pjId,
            companyId: currentUser.coId
        },
        closeThrowErr: true
    })
}
export function getEpidemicManageApi(personType: number | string) {  //防疫管理
    const { request, } = window.PMHOME;
    const currentUser = getCurrentUser();
    return request(`${service.epidemicServer}/epidemic/getEpidemicManagement`, {
        method: 'POST',
        data: {
            personType,
            projectId: currentUser.pjId,
            companyId: currentUser.coId
        },
        closeThrowErr: true
    })
}
export function getPartitionStatisticsCountApi() {  //分区现场
    const { request, } = window.PMHOME;
    const currentUser = getCurrentUser();
    return request(`${service.zhgdPerson}/attendance/statisticalArea/listStatisticalAreaAndNumber`, {
        method: 'GET',
        headers:{
            accept: "application/json, text/plain, */*",
            'content-type':'application/json'
        },
        params: {
            projectId: currentUser.pjId,
            companyId: currentUser.coId
        }
    })
}
export function getAttendanceHomepageStatistic() {  //新分区现场
    const { request, } = window.PMHOME;
    const currentUser = getCurrentUser();
    return request(`${service.zhgdPerson}/attendance/homepage/partition/worker/statistic`, {
        method: 'POST',
        // headers:{
        //     accept: "application/json, text/plain, */*",
        //     'content-type':'application/json'
        // },
        data: {
            projectId: currentUser.pjId,
            companyId: currentUser.coId
        }
    })
}
export function getHomepageOverview(personType: number | string) {  //新防疫管理
    const { request, } = window.PMHOME;
    const currentUser = getCurrentUser();
    return request(`${service.zhgdPerson}/lw/homepage/epidemic/overview`, {
        method: 'POST',
        data: {
            personType,
            projectId: currentUser.pjId,
            companyId: currentUser.coId
        },
        closeThrowErr: true
    })
}

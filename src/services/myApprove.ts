// import server from './service';
import { getCurrentUser } from './api';

// 我的审批
export async function getApproveInfo() {
  const currentUser = getCurrentUser();
  const { request } = window.PMHOME;
  return request(
    `/bim/api/front/mechatronics/findToDoList?projectId=${currentUser.pjId}&userId=${currentUser.mid}`,
    {
      method: 'GET',
    },
  );
}

// 审批统计
export async function getApprove() {
  const currentUser = getCurrentUser();
  const { request } = window.PMHOME;
  return request(
    `/bim/api/front/mechatronics/flowCount?projectId=${currentUser.pjId}&userId=${currentUser.mid}`,
    {
      method: 'GET',
    },
  );
}

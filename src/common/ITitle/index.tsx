import React from "react";
import styles from './index.less';
import Rect from "./icon/Rect";

interface IProps {
  children?: any
  title?: React.ReactNode;
  titleRight?: React.ReactNode;
  hiddenBody?: boolean;
  wrapperStyle?: React.CSSProperties;
  bodyStyle?: React.CSSProperties;
  style?: React.CSSProperties;
}

/**
 * 左边带蓝边的标题
 * @constructor
 */
export default function ITitle (props: IProps) {
  return (
    <div style={props?.style}>
      <div className={styles.wrapper} style={props?.wrapperStyle}>
        <div className={styles.inner}>
          <Rect />
          <div style={{display: "inline-block", verticalAlign: 'middle'}}>{props.title}</div>
          <div className={styles.innerRight}>
            {props.titleRight}
          </div>
        </div>
      </div>
      <div style={{display: props.hiddenBody ? "none" : "block", ...props?.bodyStyle}}>
        {props.children}
      </div>
    </div>
  )
}

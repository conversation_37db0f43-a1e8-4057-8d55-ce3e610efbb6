import opts, {axis, title, legend, color, grid} from '@/panel/echartOptions';
import React from 'react';
import ReactEcharts from 'echarts-for-react';
import EmptyCenter from '@/components/EmptyCenter';

interface LProps {
    data: any[];
    category: any[];
}

export default (props: LProps) => {

    const { data, category } = props;

    const echartsOption = {
        title: {
          text: '近7日监控在线率趋势',
          ...title,
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
          },
          formatter: (params: any[]) => {
            const param = params[0];
            const marker = param.marker;
            return `<div>
                <span>${param.name}</span></br>
                <span>${marker}${param.value} %</span>
            </div>`
          }
        },
        grid,
        color,
        xAxis: [
          {
            type: 'category',
            ...axis,
            data: category
          }
        ],
        yAxis: [
          {
            type: 'value',
            ...axis,
            name: '单位：%',
            nameTextStyle: {
                color: '#666666'
            }
          }
        ],
        series: [
          {
            type: 'line',
            smooth: true,
            areaStyle: {
                color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [{
                        offset: 0, color: 'rgba(3,185,134, .1)' // 0% 处的颜色
                    }, {
                        offset: 1, color: '#FFFFFF' // 100% 处的颜色
                    }],
                }
            },
            data
          },
        ]
      }

    return (<div style={{position: 'relative', height: '100%'}}>
        { data.length ? <ReactEcharts
          style={{height: '96%'}}
          opts={opts}
          option={echartsOption}
          theme="clear"/> : <EmptyCenter description="暂无监控趋势数据"/>
        }
    </div>);
}
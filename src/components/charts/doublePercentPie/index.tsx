import React, { useState, useEffect } from 'react';
import ReactEcharts from 'echarts-for-react';
import { PieProps } from './type';
import {
  angleAxis,
  radiusAxis,
  childOpt,
  backgroundOpt,
  parentOpt,
  gaugeOpt,
} from '../percentPie/pieOption';

// 双百分比 饼图
export default (props: PieProps) => {
  const [optionData, setOptionData] = useState({} as PieProps);

  const formatOption = (params: PieProps) => {
    const { opts, data } = params;
    const { title, subTitle, titleColor, subTitleColor, titleFont, subTitleFont, seriesColor } =
      opts || {};
    return {
      title: {
        text: title || '',
        subtext: subTitle || '',
        top: '40%',
        left: 'center',
        textStyle: {
          color: titleColor || '#191919',
          fontSize: titleFont || 30,
        },
        subtextStyle: {
          color: subTitleColor || '#666',
          fontSize: subTitleFont || 14,
        },
      },
      angleAxis,
      radiusAxis,
      polar: {
        center: ['50%', '50%'],
        radius: '108%',
      },
      series: [
        Object.assign(parentOpt, { barWidth: 5 }),
        {
          type: 'bar',
          data: [
            {
              name: 'percent',
              value: (data && data[0]) || 0,
              itemStyle: {
                normal: {
                  color: (seriesColor && seriesColor[0]) || '#00C58E',
                },
              },
            },
          ],
          ...Object.assign(childOpt, { barWidth: 12, barGap: '-140%', }),
        },
        {
          name: 'percent1',
          type: 'pie',
          radius: ['73%', '78%'],
          center: ['50%', '50%'],
          clockwise: false,
          data: [
            {
              hoverOffset: 1,
              value: (data && data[1]) || 0,
              itemStyle: {
                color: '#FFBD3F',
              },
              label: {
                show: false,
              },
              labelLine: {
                normal: {
                  smooth: true,
                  lineStyle: {
                    width: 0,
                  },
                },
              },
              hoverAnimation: false,
            },
            {
              label: {
                show: false,
              },
              labelLine: {
                normal: {
                  smooth: true,
                  lineStyle: {
                    width: 0,
                  },
                },
              },
              value: 100 - (data && data[1]) || 0,
              hoverAnimation: false,
              itemStyle: {
                normal: {
                  color: '#edf4ff',
                },
                emphasis: {
                  color: '#edf4ff',
                },
              },
            },
          ],
          z: 8,
        },
        Object.assign(backgroundOpt, { radius: ['0%', '44%'] }),
        gaugeOpt,
      ],
    };
  };

  useEffect(() => {
    setOptionData(props);
  }, [props]);

  return (
    <ReactEcharts style={{ width: '100%', height: '100%' }} option={formatOption(optionData)} />
  );
};

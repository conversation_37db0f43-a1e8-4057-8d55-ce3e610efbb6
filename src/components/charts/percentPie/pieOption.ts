export const angleAxis = {
  max: 100,
  clockwise: false,
  axisLine: {
    show: false,
  },
  axisTick: {
    show: false,
  },
  axisLabel: {
    show: false,
  },
  splitLine: {
    show: false,
  },
};

export const radiusAxis = {
  type: 'category',
  axisLine: {
    show: false,
  },
  axisTick: {
    show: false,
  },
  axisLabel: {
    show: false,
  },
  splitLine: {
    show: false,
  },
};

export const childOpt = {
  coordinateSystem: 'polar',
  roundCap: true,
  barWidth: 12,
  barGap: '-100%',
  z: 2,
};

export const backgroundOpt = {
  type: 'pie',
  z: 4,
  radius: ['0%', '55%'],
  center: ['50%', '50%'],
  data: [
    {
      label: {
        normal: {
          position: 'inner',
          show: false,
        },
      },
      labelLine: {
        show: false,
      },
      itemStyle: {
        normal: {
          color: '#edf4ff',
        },
        emphasis: {
          color: '#edf4ff',
        },
      },
      value: 100,
      hoverAnimation: false,
    },
  ],
};

export const parentOpt = {
  type: 'bar',
  data: [
    {
      value: 100,
      itemStyle: {
        normal: {
          color: '#edf4ff',
        },
        emphasis: {
          color: '#edf4ff',
        },
      },
      hoverAnimation: false,
    },
  ],
  coordinateSystem: 'polar',
  roundCap: true,
  barWidth: 7,
  barGap: '-70%',
  z: 1,
};

// 饼图 点状环形
export const gaugeOpt = {
  type: 'gauge',
  radius: '66%',
  startAngle: '0',
  endAngle: '-359.99',
  splitNumber: 40,
  center: ['50%', '50%'],
  pointer: {
    show: false,
  },
  title: {
    show: false,
  },
  detail: {
    show: false,
  },
  data: [
    {
      value: 95,
      name: '',
    },
  ],
  axisLine: {
    lineStyle: {
      width: 20,
      opacity: 0,
    },
  },
  axisTick: {
    show: false,
  },
  splitLine: {
    show: true,
    length: 4,
    lineStyle: {
      color: {
        type: 'linear',
        x: 1,
        y: 0,
        x2: 0,
        y2: 0,
        colorStops: [
          {
            offset: 0,
            color: '#fff',
          },
          {
            offset: 0.5,
            color: '#e8e8e8',
          },
          {
            offset: 1,
            color: '#e8e8e8',
          },
        ],
        globalCoord: false,
      },
      width: 1,
      type: 'solid',
    },
  },
  axisLabel: {
    show: false,
  },
  z: 10,
};

import React, { useEffect, useState } from 'react';
import ReactEcharts from 'echarts-for-react';
import { PieProps } from './type';
import { angleAxis, radiusAxis, childOpt, backgroundOpt, parentOpt } from './pieOption';

// 百分比饼图
export default (props: PieProps) => {
  const [optionData, setOptionData] = useState({} as PieProps);

  const formatOpts = (params: PieProps) => {
    const { opts, data } = params;
    const { title, subTitle, titleColor, subTitleColor, titleFont, subTitleFont, labelColor } =
      opts || {};

    return {
      title: {
        text: title || '',
        subtext: subTitle || '',
        top: '40%',
        left: 'center',
        textStyle: {
          color: titleColor || '#191919',
          fontSize: titleFont || 32,
        },
        subtextStyle: {
          color: subTitleColor || '#666',
          fontSize: subTitleFont || 14,
        },
      },
      angleAxis,
      radiusAxis,
      polar: {
        center: ['50%', '50%'],
        radius: '135%',
      },
      series: [
        {
          type: 'bar',
          data: [
            {
              name: 'percent',
              value: data,
              itemStyle: {
                width: 10,
                color: labelColor || '#3996FF',
              },
            },
          ],
          ...childOpt,
        },
        parentOpt,
        {
          ...backgroundOpt,
          radius: ['0%', '58%'],
        },
      ],
    };
  };

  useEffect(() => {
    setOptionData(props);
  }, [props]);

  return (
    <ReactEcharts
      style={{ width: '100%', height: '100%' }}
      option={formatOpts(optionData)}
      theme="clear"
    />
  );
};

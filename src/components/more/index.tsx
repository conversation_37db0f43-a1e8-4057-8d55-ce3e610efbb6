/*
 * @Author: wendy
 * @Date: 2020-08-27 10:35:51
 * @LastEditors: wendy
 * @LastEditTime: 2020-08-27 13:46:19
 * @Description: 通用组件（面板右上角 点击查看更多）
 */
import Styles from './index.less';
import React from 'react';

interface IProps {
  content?: string
  jumpUrl: string
  className?: string
  // 跳转方式：默认当前窗口内跳转
  target?: string
  // 自定义点击事件
  onClick?: (url: string) => void
}

export default (props: IProps) => {
  const {content, jumpUrl, className, target, onClick} = props

  const handleJump = () => {
    // 自定义点击事件
     if (onClick) return onClick(jumpUrl)

    if (target === '_blank') {
      // 开新窗口
      window.open(jumpUrl)
    } else {
      push(jumpUrl)
    }
  }

  return (
    <div
      className={`${Styles.moreWrapper} ${className}`}
      onClick={handleJump}
    >
      {content || '详情'}
      <span className={Styles.arrow} />
    </div>
  )
}


export function push (url:string) {
  // 默认页面内跳转
  const [_, consoleName, value] = url.split('/');
  // 是否微前端
  const isConsole = consoleName === 'console';
  if (isConsole) {
    window.PMHOME.routes.push('', url)
  } else {
    window.location.replace(url)
  }
}

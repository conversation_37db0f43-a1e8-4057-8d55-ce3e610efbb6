import React from 'react'
import { Empty } from 'antd'

interface ECProps {
    description: string;
    image?: React.ReactNode;
}

export default (props: ECProps) => {

    const { image, description } = props;

    return <div style={{position: 'absolute', top: '50%', left: '50%', transform: 'translate(-50%, -50%)'}}>
        <Empty image={image ? image : Empty.PRESENTED_IMAGE_SIMPLE} description={description} />
    </div>
}
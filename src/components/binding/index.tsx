import React, {useState} from 'react';
import Styles from './index.less';
import { Modal, Button } from 'antd';
import useMount from "react-use/esm/useMount";

export default () => {
  const {getCurrentUser} = window.PMHOME;
  const currentUser = getCurrentUser();
  const [visible, setVisible] = useState(false)

  useMount(() => {
    if (currentUser.emailStatus === 2 && currentUser.mobileStatus === 2) {
      setVisible(true)
    }
  })

  const close  = () => {
    setVisible(false)
  }
  const bindingPhone = () => {
    window.open(`/passport/#/passport/information?dialog=phone`);
  }
  const bindingEmail = () => {
    window.open(`/passport/#/passport/information?dialog=email`);
  }

  return (
    <Modal
      title={'提示'}
      onCancel={close}
      footer={null}
      visible={visible}
      width={275}
    >
      <div className={Styles.handleInfo}>
        <p>尊敬的用户：</p>
        <p className={Styles.info}>您好，为了您的帐号安全，请绑定邮箱或手机。</p>
        <Button className={Styles.handlePhone} type="primary" onClick={bindingPhone}>绑定手机</Button>
        <Button type="ghost" onClick={bindingEmail}>绑定邮箱</Button>
      </div>
    </Modal>
  )
}

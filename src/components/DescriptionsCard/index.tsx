import React, { CSSProperties } from "react";
import Styles from './index.less';

interface IData {
  value: number | string | React.ReactNode,
  label: string;
  img?: {
    imgUrl: string;
    width: number;
    height: number;
  };
  numStyle?: {
    color?: string;
    fontSize?: number;
  },
  tipStyle?: {
    color?: string;
    fontSize?: number;
  },
  tooltipNode?: React.ReactNode;
}
interface IProps {
  data: IData[];
  style?: CSSProperties;
  className?: string;
}

export default (props: IProps) => {
  const {className} = props;
  
  return (
    <div className={`${Styles.descriptionsCard} ${className}`} style={{...props.style}}>
      {
        props.data.map((item, index) => {
          return (
            <div key={index} style={{display: 'flex', justifyContent: 'center', alignItems: 'center'}}>
              {item.img ?
                <img
                  src={item.img.imgUrl}
                  alt={item.label}
                  width={item.img.width}
                  height={item.img.height ? item.img.height : '100%'}
                  style={{marginRight: 10}}
                /> : ''
              }
              <div key={index}>
                <div className={Styles.descriptionsNum} style={{...item.numStyle}}>{item.value}</div>
                <div className={Styles.descriptionsTip} style={{...item.tipStyle}}>{item.label} {item.tooltipNode ?? ''}</div>
              </div>
            </div>
          )
        })
      }
    </div>
  )
}

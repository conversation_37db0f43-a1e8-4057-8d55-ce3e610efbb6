.descriptionsCard {
  width: 100%;
  height: 100%;
  display: flex;
  padding: 0 0 15px 0;
  > div{
    flex: 1;
    &:nth-last-child(3) {
      color: #00C586;
    }
    &:nth-last-child(2) {
      color: #3996FF;
    }
    &:nth-last-child(1) {
      color: #FFA200;
    }
    > div{
      text-align: center;
      .descriptionsNum{
        font-size: 24px;
      }
      .descriptionsTip{
        color: #666;
        margin-top: 5px;
      }
    }
  }
}

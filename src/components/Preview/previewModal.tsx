import React, { useState, useEffect } from "react"
import { Modal} from 'antd'
import {fileDownloadUrl} from '@pms/react-utils'

interface FileObj {
  uuid: string,
  type: number,
  fileUuid: string
}

interface Item {
  fileObj: FileObj,
  close: () => void
}

export default (props: Item) => {
  const [url, setUrl] = useState('')

  useEffect(() => {
    getUrl()
  }, [])
  // 图片，3视频，2音频弹窗
  const getUrl = () => {
    fileDownloadUrl(1, props.fileObj.fileUuid).then((url: string) => {
        setUrl(url)
      })
  }

  return (
    <>
    <Modal
      width={props.fileObj.type === 2 ? 350 : 800}
      title="预览"
      visible={true}
      onCancel={props.close}
      footer={null}
    >
      {
        props.fileObj.type === 2 ?
        <audio src={url} autoPlay controls >您的浏览器不支持音频</audio> : ''
      }
      {
        props.fileObj.type === 3 ?
        <video muted autoPlay controls style={{width: '100%', height: '100%', overflow: 'hidden'}} src={url} >
        </video> : ''
      }
    </Modal>
    </>
  )
}

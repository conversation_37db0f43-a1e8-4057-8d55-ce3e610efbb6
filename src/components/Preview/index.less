@import "~@/primary.less";
.mainCon {
  margin-bottom: 20px;
}
.fileCon {
  color: #fff;
  text-align: center;
  margin-right: 10px;
}
.handleCon {
  div {
    span {
      cursor: pointer;
      color: @primary-color;
      display: inline-block;
      width: 50%;
      text-align: left;
    }
  }
}
.imgCon {
  overflow: hidden;
  width: 40px;
  height: 40px;
  margin-right: 10px;
  .preview-img {
    width: 40px;
    height: 40px;
    background:#ddd;
  }
}
.videoCon {
  width: 40px;
  height: 40px;
  background: #f5b904;
  margin-right: 10px;
  color: #fff;
  text-align: center;
  line-height: 40px;
}
.audioCon {
  width: 180px;
  border: 1px solid #e1ebe8;
  display: block;
  height: 36px;
  cursor: pointer;
  line-height: 36px;
}
.filesCon {
  width: 40;
  height: 40;
  text-align: center;
  line-height: 40px;
  overflow: hidden;
  color: '#fff';
}
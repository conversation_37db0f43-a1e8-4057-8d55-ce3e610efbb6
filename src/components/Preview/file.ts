const previewFileTypes = new Set([
  'application/pdf',
  'application/msword',
  'application/vnd.ms-wordml',
  'application/vnd.ms-excel',
  'application/octet-stream',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'text/plain',
  'text/html',
  'application/vnd.openxmlformats-officedocument.presentationml.presentation',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'application/vnd.ms-powerpoint',
  'application/x-tika-ooxml',
  'application/x-tika-msoffice',
  'application/xml',
  'application/zip',
  'application/x-zip-compressed',
  'application/7z',
  'application/rar',
  'application/wps-writer',
  'doc',
  'docx',
  'ppt',
  'pptx',
  'txt',
  'pdf',
  'xls',
  'xlsx',
  'zip',
  'rar',
  '7z',
  'wps'
])
const previewFile = function (node: any) {
  const mime = node.mime || node.fileMime
  const fileName = node.name || node.docName
  if (fileName.includes('.')) {
    const length = fileName.lastIndexOf('.')
    return previewFileTypes.has(fileName.slice(length + 1))
  } else {
    return previewFileTypes.has(mime)
  }
}

export default previewFile

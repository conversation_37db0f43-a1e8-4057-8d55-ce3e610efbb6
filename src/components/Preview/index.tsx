import React, { useState, useEffect } from "react"
import {<PERSON><PERSON><PERSON>, Button} from 'antd'
import Styles from './index.less'
import {FolderOutlined} from '@ant-design/icons';
import IconColor from './iconColor'
import previewFile from './file'
import {fileDownloadUrl} from '@pms/react-utils'
import PreviewModal from './previewModal'
// @ts-ignore
import { RViewer, RViewerTrigger } from 'react-viewerjs'
import { preview } from '@pms/react-upload'

const {Text} = Typography

interface Props {
  item: any,
  defaultUrl?: string,
  items?: [],
  margin?: string,
  info?: boolean,
  imgWidth?: string,
  width?: number,
  height?: number,
  infoWidth?: number,
  isAuth?: boolean,
  multiple?: boolean,
  isConst?: boolean,
  isFilling?: boolean,
  th?: number
}

export default (props: Props) => {
  const [isPreview, setIsPreview] = useState(false)
  const [src, setSrc] = useState('')
  const [bigsrc, setBigSrc] = useState('')
  const [filetype, setFileType] = useState('')
  const [visible, setVisible] = useState(false)
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    ready()
  }, [])

  const ready = () => {
    getSourceImg()
    computedType()
    isPreviewHandle()
    gettype()
  }

  const gettype = () => {
    if (props.item) {
      let fileName = props.item.name || props.item.fileName || props.item.docName
      const length = fileName.lastIndexOf('.')
      setFileType(fileName.slice(length + 1))
    }
  }

  const computedType = () => {
    switch (props.item.type) {
      case 1:
        props.item.typeText = '预览'
        break
      case 2:
        props.item.typeText = '播放'
        break
      case 3:
        props.item.typeText = '播放'
        break
      case 4:
        props.item.typeText = '预览'
        let fileName = props.item.name || props.item.fileName || props.item.docName
        const url = fileName.slice(fileName.lastIndexOf('.') + 1).toLowerCase()
        props.item.bgColor = IconColor(url)
        break
      default:
        props.item.documentClassify = true
    }
  }

  // 获取图片地址
  const getSourceImg = () => {
    if (props.item.type === 1 && props.item.fileUuid) {
      fileDownloadUrl(4, props.item.fileUuid).then((url: string) => {
        setSrc(url)
      })
    }
  }

  const isPreviewHandle = () => {
    if (props.item.type === 4) {
      setIsPreview(previewFile(props.item))
    } else {
      setIsPreview(true)
    }
  }

  // 下载
  const downFile = () => {
    // 获取下载地址
    if (props.item.fileUuid) {
      fileDownloadUrl(1, props.item.fileUuid).then((url: string) => {
        const elemIF = document.createElement('iframe')
        elemIF.src = url
        elemIF.style.display = 'none'
        document.body.appendChild(elemIF)
        setTimeout(() => {
          document.body.removeChild(elemIF)
        }, 300)
      })
    }
  }


  // 图片
  const getImgCon = () => {
    if (props.item.type !== 1) {
      return
    }
    return (
      <>
      <div
        className={Styles.imgCon}>
        <img
          alt=''
          src={src}
        />
        <RViewer imageUrls={bigsrc}>
          <RViewerTrigger>
            <Button id="preImg"/>
          </RViewerTrigger>
        </RViewer>
      </div>
      </>
    )
  }

  // 音频
  const getAudioCon = () => {
    if (props.item.type !== 2) {
      return
    }
    return (
      <div
        className={Styles.audioCon}
        style={{
          background: props.item.bgColor,
          width: props.width || 40,
          height: props.width || 40,
          lineHeight: `${props.width || 40}px`
        }}
      >
        {filetype}
      </div>
    )
  }

  // 视频
  const getVideoCon = () => {
    if (props.item.type !== 3) {
      return
    }
    return (
      <div className={Styles.videoCon}>mp4</div>
    )
  }

  // 固定显示文件夹
  const getFilesCon = () => {
    if (props.item.documentClassify) {
      return (
        <div
          className={Styles.filesCon}
          style={{
            background: props.item.bgColor ? props.item.bgColor : '#ddd'
          }}
        >
          <FolderOutlined />
        </div>
      )
    }
  }

  // 普通文件
  const getFileCon = () => {
    if (props.item.type === 2 || props.item.type === 1 || props.item.type === 3 || props.item.documentClassify) {
      return
    }
    return (
    <div
      className={Styles.fileCon}
      style={{
        background: props.item.bgColor,
        width: props.width || 40,
        height: props.width || 40,
        lineHeight: `${props.width || 40}px`
      }}
    >
      {filetype}
    </div>
    )
  }

  const getHandleCon = () => {
    if (props.item.type === 2 ) {
      return
    }
    return (
      <div className={Styles.handleCon}>
        <Text>{props.item.fileName || props.item.name} ({props.item.fileSize}kb)</Text>
        <div style={{width: 130}}>
          <span onClick={downFile}>下载</span>
          {
            isPreview ?
            <span onClick={() => previewFun(props.item)}>{props.item.typeText}</span> : ''
          }
        </div>
      </div>
    )
  }

  // 预览文件
  const previewFun = (item: any) => {
    switch (item.type || item.fileType) {
      case 1:
        previewImage()
        break
      case 2:
        // 音频
        setVisible(true)
        break
      case 3:
        // 播放视频
        setVisible(true)
        break
      case 4:
        if (isPreview) {
          // 预览文件
          preview(item)
        }
        break
    }
  }

  // 预览图片
  const previewImage = () => {
    if (loading) {return}
    setLoading(true)
    fileDownloadUrl(1, props.item.fileUuid).then((url: string) => {
      setBigSrc(url)
      // @ts-ignore
      document.getElementById('preImg').click()
      setLoading(false)
    })
  }

  return (
    <>
    {
      props.item ?
      <div className={Styles.mainCon}>
        {/* 显示图片 */}
        {
          getImgCon()
        }
        {/* 显示音频文件 */}
        {
          getAudioCon()
        }
        {/* 显示视频文件 */}
        {
          getVideoCon()
        }
        {/* 是否固定显示文件夹 */}
        {
          getFilesCon()
        }
        {/* 显示普通文件 */}
        {
          getFileCon()
        }
        {/* 显示右侧操作栏 */}
        {
          getHandleCon()
        }
      </div> :
      <img alt='' style={{width: 40, height: 40}} src="./news_default.png" />
    }
    {
      visible ?
      <PreviewModal fileObj={props.item} close={() => {setVisible(false)}}/> : ''
    }
    </>
  )
}

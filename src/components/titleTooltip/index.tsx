import { InfoCircleOutlined } from '@ant-design/icons';
import { Tooltip } from 'antd';
import { TooltipPlacement } from 'antd/es/tooltip';
import React from 'react';
import styles from './index.less';

type Props = {
  text: string;
  placement?: TooltipPlacement;
  left?: number;
};

const TitleTooltip = (props: Props) => {
  const { text, placement = 'top', left = 100 } = props;
  return (
    <div className={styles.tooltip} style={{ left }}>
      <Tooltip placement={placement} title={text}>
        <InfoCircleOutlined />
      </Tooltip>
    </div>
  );
};

export default TitleTooltip;

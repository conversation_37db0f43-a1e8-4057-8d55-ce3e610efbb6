import { IConfig } from '@umijs/types';
import chainWebpack from './config/.umirc.chainWebpack';
import proxy from './config/.umirc.proxy';
import reactOptions from './config/.umirc.react';
import routes from './config/.umire.routes';

const config: IConfig = {
  alias: {
    '@':'/src'
  },
  //别名配置
  hash: true,
  //配置是否让生成的文件包含 hash 后缀，通常用于增量发布和避免浏览器加载缓存
  //两种类型 配置式路由和约定式路由
  routes,
  //umi 的路由基于 react-router 实现，配置和 react-router@4 基本一致
  //component 指向的路由组件文件是从 src/pages 目录开始解析的
  //如果配置了 routes，则优先使用配置式路由，且约定式路由会不生效
  targets: { ie: 11 },
  //配置浏览器最低版本，会自动引入 polyfill 和做语法转换，配置的 targets 会和合并到默认值，所以不需要重复配置。
  devServer: { https: false },
  proxy,
  //配置插件列表,数组项为指向插件的路径，可以是 npm 依赖、相对路径或绝对路径。如果是相对路径，则会从项目根目录开始找
  ...reactOptions,
  base: '/console/panel',
  //配置项目的根目录 官方解释:你有路由 / 和 /users，然后设置了 base 为 /foo/，那么就可以通过 /foo/ 和 /foo/users 访问到之前的路由 修改后,项目打包后的文件引用还是使用的'/'路径
  manifest: { basePath: './panel' },
  //配置后会生成 asset-manifest.json，option 传给 https://www.npmjs.com/package/webpack-manifest-plugin
  publicPath: process.env.CDN_ZZ ? `${process.env.CDN_ZZ}/panel/` : `/panel/`,
  //配置webpack的publicPath.当打包的时候,webpack会在静态文件路径前面添加publicPath的值(接base)
  outputPath: '/dist/front',
  //默认情况下项目及其public下的所有文件在打包时会放进dist目录 项目打包后会输出到该目录,不用手动创建
  // ignoreMomentLocale: true,
  //忽略 moment 的 locale 文件，用于减少尺寸
  history: { type: 'browser' },
  //指定 history 类型，可选 browser、hash 和 memory。
  title: false,
  //页面标题
  locale: {
    default: 'zh-CN',
    antd: true,
  },
  lessLoader: {
    javascriptEnabled: true,
  },
  qiankun: {
    slave: {},
  },
  dynamicImport: {
    loading: '@/pages/loading.tsx',
  },
  chainWebpack,
  headScripts:
    process.env.NODE_ENV === 'production'
      ? []
      : ['https://zz-test05.pinming.org/console/static/cn.pinming.console.umd.development.js'],
};

export default config;

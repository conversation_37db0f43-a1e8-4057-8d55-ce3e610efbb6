{"name": "panel", "version": "0.0.1", "description": "jyd", "author": "jyd", "main": "/src/index.ts", "private": false, "scripts": {"start": "cross-env environment=05 umi dev", "start-test01": "cross-env environment=01 umi dev", "start-test02": "cross-env UMI_UI=1 environment=02 umi dev", "start-test03": "cross-env environment=03 umi dev", "start-test04": "cross-env environment=04 umi dev", "start-test05": "cross-env environment=05 umi dev", "build": "umi build && npm run copy-index", "copy-index": "node ./script/copy.ts", "build-bundleAnalyzer": "cross-env bundleAnalyzer=true umi build", "test": "umi test", "lint:es": "eslint --ext .js src mock tests", "lint:ts": "tslint \"src/**/*.ts\" \"src/**/*.tsx\"", "precommit": "lint-staged"}, "dependencies": {"@ant-design/icons": "^4.0.2", "@ant-design/plots": "^1.2.5", "@ant-design/pro-layout": "4.10.2", "@ant-design/pro-table": "1.0.22", "@babel/plugin-syntax-typescript": "^7.16.5", "@pms/backbone-map-dom": "^0.0.83", "@pms/console": "1.1.91", "@pms/pmserver": "1.0.8", "@pms/react-auth": "1.0.5", "@pms/react-current-user": "1.0.5", "@pms/react-retrieve": "4.0.87", "@pms/react-upload": "1.0.8", "@pms/react-utils": "1.0.11", "@sindresorhus/is": "^4.0.1", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "ahooks": "^3.3.0", "antd": "4.6.0", "antd-theme-webpack-plugin": "^1.3.6", "array-move": "^3.0.0", "copy-webpack-plugin": "^4.6.0", "core-js": "^3.20.0", "css-vars-ponyfill": "^2.3.2", "dva": "^2.4.0", "echarts": "^4.8.0", "echarts-for-react": "^2.0.16", "eslint-plugin-react-hooks": "^4.2.0", "lodash": "^4.17.21", "moment": "^2.22.2", "qs": "^6.9.4", "react": "^16.8.6", "react-countup": "^4.3.3", "react-custom-scrollbars": "^4.2.1", "react-dom": "^16.8.6", "react-router": "^5.2.1", "react-router-dom": "^5.3.0", "react-sortable-hoc": "^1.11.0", "react-upload": "^0.0.3", "react-use": "^11.3.2", "react-viewerjs": "^0.1.9", "redux": "^4.0.1"}, "devDependencies": {"@pms/react-websocket": "1.0.5", "@types/jest": "^26.0.23", "@types/js-md5": "^0.4.2", "@types/jsonp": "^0.2.0", "@types/lodash": "^4.14.137", "@types/react": "^16.7.18", "@types/react-dom": "^16.0.11", "@types/react-test-renderer": "^16.0.3", "@umijs/plugin-qiankun": "2.4.1", "@umijs/preset-react": "^1.4.6", "@umijs/preset-ui": "^2.0.10", "@umijs/types": "^3.2.19", "babel-eslint": "^9.0.0", "cross-env": "^5.2.0", "eslint": "^5.4.0", "eslint-config-umi": "^1.4.0", "eslint-plugin-flowtype": "^2.50.0", "eslint-plugin-import": "^2.14.0", "eslint-plugin-jsx-a11y": "^5.1.1", "eslint-plugin-react": "^7.11.1", "filemanager-webpack-plugin": "^2.0.5", "fs-extra": "10.1.0", "husky": "^0.14.3", "lint-staged": "^7.2.2", "react-test-renderer": "^16.7.0", "tslint": "^5.12.0", "tslint-eslint-rules": "^5.4.0", "tslint-react": "^3.6.0", "umi": "3.3.9", "umi-request": "^1.3.5", "webpack-bundle-analyzer": "^3.4.1"}, "lint-staged": {"*.{ts,tsx}": ["tslint --fix", "git add"], "*.{js,jsx}": ["eslint --fix", "git add"]}, "engines": {"node": ">=8.0.0"}}